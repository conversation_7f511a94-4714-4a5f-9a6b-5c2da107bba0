import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

from fastapi import <PERSON><PERSON>outer, BackgroundTasks, Depends, HTTPException, Request, Response
from fastapi.responses import StreamingResponse
from fastapi.security import HTTPAuthorizationCredentials

from app.core.security import get_current_user, http_bearer
from app.schemas.agent import ChatRequest, ChatResponse
from app.services.chat_service import chat_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/chat", response_model=ChatResponse)
async def chat_with_agent(
    chat_request: ChatRequest,
    response: Response,
    background_tasks: BackgroundTasks,
    fastapi_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Chat with an AI agent
    """
    try:
        access_token = credentials.credentials if credentials else None
        session_id = chat_request.session_id or fastapi_request.cookies.get(
            "sessionId")

        if not session_id:
            session_id = str(uuid.uuid4())
            response.set_cookie(
                key="sessionId",
                value=session_id,
                httponly=True,
                samesite="lax",
                expires=datetime.now(
                    timezone.utc) + timedelta(hours=24),
            )

        result = await chat_service.chat(
            message=chat_request.message,
            session_id=session_id,
            agent_type=chat_request.agent_type,
            user_id=current_user["id"],
            background_tasks=background_tasks,
            access_token=access_token
        )

        if not result.get("success"):
            raise HTTPException(status_code=500, detail=result.get(
                "error", "Agent failed to process the request."))

        agent_type_str = result["agent_type"]
        agent_type_mapping = {
            "HM Hotel Manager": "hotel_manager",
            "hotel_triage": "hotel_manager",
            "hotel_listing_specialist": "hotel_manager",
            "price_update_specialist": "hotel_manager",
            "room_info_specialist": "hotel_manager",
            "form_submission_processor": "hotel_manager",
            "clarification_specialist": "hotel_manager",
            "Smart Response Agent": "form_response"
        }
        agent_type_str = agent_type_mapping.get(
            agent_type_str, agent_type_str)

        return ChatResponse(
            response=result["response"],
            session_id=session_id,
            agent_type=agent_type_str,
            metadata=result.get("metadata")
        )
    except Exception as e:
        logger.error(f"Error in chat_with_agent: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Agent error: {str(e)}")


@router.post("/chat/stream")
async def chat_with_agent_stream(
    chat_request: ChatRequest,
    response: Response,
    background_tasks: BackgroundTasks,
    fastapi_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Chat with an AI agent with real-time status updates via Server-Sent Events
    """
    async def generate_status_stream():
        try:
            yield f"data: {json.dumps({'type': 'status', 'message': 'Initializing chat session...'})}\n\n"
            await asyncio.sleep(0.1)

            access_token = credentials.credentials if credentials else None
            session_id = chat_request.session_id or fastapi_request.cookies.get(
                "sessionId")

            if not session_id:
                session_id = str(uuid.uuid4())
                yield f"data: {json.dumps({'type': 'status', 'message': 'Creating new session...'})}\n\n"
                await asyncio.sleep(0.1)
                response.set_cookie(
                    key="sessionId",
                    value=session_id,
                    httponly=True,
                    samesite="lax",
                    expires=datetime.now(
                        timezone.utc) + timedelta(hours=24),
                )
            else:
                yield f"data: {json.dumps({'type': 'status', 'message': 'Using existing session...'})}\n\n"
                await asyncio.sleep(0.1)

            async for event in chat_service.chat_streamed(
                message=chat_request.message,
                session_id=session_id,
                agent_type=chat_request.agent_type,
                user_id=current_user["id"],
                background_tasks=background_tasks,
                access_token=access_token
            ):
                if event['type'] == 'response':
                    agent_type_str = event['data']['agent_type']
                    agent_type_mapping = {
                        "HM Hotel Manager": "hotel_manager",
                        "hotel_triage": "hotel_manager",
                        "hotel_listing_specialist": "hotel_manager",
                        "price_update_specialist": "hotel_manager",
                        "room_info_specialist": "hotel_manager",
                        "form_submission_processor": "hotel_manager",
                        "clarification_specialist": "hotel_manager",
                        "Smart Response Agent": "form_response"
                    }
                    event['data']['agent_type'] = agent_type_mapping.get(
                        agent_type_str, agent_type_str)

                yield f"data: {json.dumps(event)}\n\n"

            yield f"data: {json.dumps({'type': 'complete'})}\n\n"

        except Exception as e:
            logger.error(
                f"Error in streaming chat: {str(e)}", exc_info=True)
            yield f"data: {json.dumps({'type': 'error', 'message': f'Agent error: {str(e)}'})}\n\n"

    return StreamingResponse(
        generate_status_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )
