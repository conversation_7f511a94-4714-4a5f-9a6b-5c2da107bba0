import logging
from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.core.security import get_current_user
from app.services.integration_db_service import (
    SyncOperationService,
    SyncQueueService,
    HotelIntegrationService
)
from app.schemas.integration import OperationStatus, OperationType

logger = logging.getLogger(__name__)
router = APIRouter()
http_bearer = HTTPBearer(auto_error=False)


@router.get("/monitoring/dashboard")
async def get_monitoring_dashboard(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get monitoring dashboard data."""
    try:
        access_token = credentials.credentials if credentials else None
        sync_service = SyncOperationService(access_token=access_token)
        queue_service = SyncQueueService(access_token=access_token)
        integration_service = HotelIntegrationService(
            access_token=access_token)

        # Get current time for calculations
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)

        # Get actual dashboard metrics
        try:
            # Get integration counts
            integrations_response = integration_service.supabase.table(
                "hotel_integrations").select("sync_status").execute()
            total_integrations = len(
                integrations_response.data) if integrations_response.data else 0
            active_integrations = len([i for i in integrations_response.data if i.get(
                "sync_status") == "active"]) if integrations_response.data else 0
            failed_integrations = len([i for i in integrations_response.data if i.get(
                "sync_status") == "error"]) if integrations_response.data else 0

            # Get pending operations count
            pending_response = queue_service.supabase.table("sync_queue").select(
                "id", count="exact").eq("status", "pending").execute()
            pending_operations = pending_response.count if pending_response.count else 0

        except Exception as e:
            logger.warning(f"Error fetching real metrics, using defaults: {e}")
            total_integrations = active_integrations = failed_integrations = pending_operations = 0

        dashboard_data = {
            "summary": {
                "total_integrations": total_integrations,
                "active_integrations": active_integrations,
                "failed_integrations": failed_integrations,
                "pending_operations": pending_operations
            },
            "recent_activity": {
                "operations_last_24h": 0,
                "successful_operations_last_24h": 0,
                "failed_operations_last_24h": 0,
                "average_processing_time": "0s"
            },
            "queue_status": {
                "pending_items": 0,
                "processing_items": 0,
                "failed_items": 0,
                "oldest_pending": None
            },
            "provider_status": {
                "booking_com": {"status": "healthy", "last_sync": None, "error_rate": 0},
                "expedia": {"status": "healthy", "last_sync": None, "error_rate": 0},
                "airbnb": {"status": "healthy", "last_sync": None, "error_rate": 0}
            }
        }

        return dashboard_data

    except Exception as e:
        logger.error(f"Error fetching monitoring dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch monitoring dashboard"
        )


@router.get("/monitoring/operations")
async def get_sync_operations(
    hotel_id: Optional[UUID] = Query(None, description="Filter by hotel ID"),
    operation_type: Optional[OperationType] = Query(
        None, description="Filter by operation type"),
    status_filter: Optional[OperationStatus] = Query(
        None, description="Filter by status"),
    limit: int = Query(50, description="Number of operations to return"),
    offset: int = Query(0, description="Number of operations to skip"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get sync operations with filtering and pagination."""
    try:
        access_token = credentials.credentials if credentials else None
        sync_service = SyncOperationService(access_token=access_token)

        # TODO: Implement filtering and pagination in the service
        # For now, get operations for specific hotel if provided
        if hotel_id:
            operations = await sync_service.get_operations_by_hotel(hotel_id, limit=limit)
        else:
            # TODO: Implement get_all_operations method
            operations = []

        # Format operations for response
        formatted_operations = []
        for op in operations:
            formatted_operations.append({
                "id": op.id,
                "hotel_integration_id": op.hotel_integration_id,
                "operation_type": op.operation_type,
                "direction": op.direction,
                "status": op.status,
                "retry_count": op.retry_count,
                "data_payload": op.data_payload,
                "external_reference": op.external_reference,
                "error_details": op.error_details,
                "scheduled_at": op.scheduled_at,
                "started_at": op.started_at,
                "completed_at": op.completed_at,
                "created_at": op.created_at,
                "duration": _calculate_duration(op.started_at, op.completed_at) if op.started_at and op.completed_at else None
            })

        return {
            "operations": formatted_operations,
            "total": len(formatted_operations),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"Error fetching sync operations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch sync operations"
        )


@router.get("/monitoring/operations/{operation_id}")
async def get_operation_details(
    operation_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get detailed information about a specific sync operation."""
    try:
        access_token = credentials.credentials if credentials else None
        sync_service = SyncOperationService(access_token=access_token)

        # TODO: Implement get_operation_by_id method
        # For now, return mock data
        operation_details = {
            "id": operation_id,
            "status": "success",
            "operation_type": "rate_update",
            "direction": "outbound",
            "retry_count": 0,
            "created_at": datetime.utcnow().isoformat(),
            "started_at": datetime.utcnow().isoformat(),
            "completed_at": datetime.utcnow().isoformat(),
            "duration": "2.5s",
            "data_payload": {
                "room_type_id": "123",
                "date_from": "2024-01-01",
                "date_to": "2024-01-07",
                "new_rate": 150.00
            },
            "error_details": None,
            "logs": [
                {
                    "timestamp": datetime.utcnow().isoformat(),
                    "level": "INFO",
                    "message": "Operation started"
                },
                {
                    "timestamp": datetime.utcnow().isoformat(),
                    "level": "INFO",
                    "message": "Sending rate update to provider"
                },
                {
                    "timestamp": datetime.utcnow().isoformat(),
                    "level": "INFO",
                    "message": "Operation completed successfully"
                }
            ]
        }

        return operation_details

    except Exception as e:
        logger.error(f"Error fetching operation details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch operation details"
        )


@router.get("/monitoring/queue")
async def get_queue_status(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get current sync queue status."""
    try:
        access_token = credentials.credentials if credentials else None
        queue_service = SyncQueueService(access_token=access_token)

        # Get pending items
        pending_items = await queue_service.get_pending_items(limit=100)

        # Calculate queue statistics
        queue_stats = {
            "total_pending": len(pending_items),
            "by_priority": {},
            "by_operation_type": {},
            "oldest_item": None,
            "average_wait_time": None
        }

        # Group by priority and operation type
        for item in pending_items:
            # By priority
            priority = item.priority
            if priority not in queue_stats["by_priority"]:
                queue_stats["by_priority"][priority] = 0
            queue_stats["by_priority"][priority] += 1

            # By operation type
            op_type = item.operation_type.value
            if op_type not in queue_stats["by_operation_type"]:
                queue_stats["by_operation_type"][op_type] = 0
            queue_stats["by_operation_type"][op_type] += 1

        # Find oldest item
        if pending_items:
            oldest_item = min(pending_items, key=lambda x: x.created_at)
            queue_stats["oldest_item"] = {
                "id": oldest_item.id,
                "created_at": oldest_item.created_at,
                "operation_type": oldest_item.operation_type,
                "priority": oldest_item.priority,
                "wait_time": _calculate_duration(oldest_item.created_at, datetime.utcnow())
            }

        return {
            "queue_statistics": queue_stats,
            "pending_items": [
                {
                    "id": item.id,
                    "hotel_id": item.hotel_id,
                    "operation_type": item.operation_type,
                    "priority": item.priority,
                    "status": item.status,
                    "attempts": item.attempts,
                    "max_attempts": item.max_attempts,
                    "scheduled_for": item.scheduled_for,
                    "created_at": item.created_at,
                    "wait_time": _calculate_duration(item.created_at, datetime.utcnow())
                }
                for item in pending_items[:20]  # Limit to first 20 for display
            ]
        }

    except Exception as e:
        logger.error(f"Error fetching queue status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch queue status"
        )


@router.get("/monitoring/health")
async def get_system_health(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get overall system health status."""
    try:
        # Check various system components
        health_status = {
            "overall_status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "database": {
                    "status": "healthy",
                    "response_time": "5ms",
                    "last_check": datetime.utcnow().isoformat()
                },
                "sync_processor": {
                    "status": "healthy",
                    "active_workers": 5,
                    "last_processed": datetime.utcnow().isoformat()
                },
                "external_apis": {
                    "booking_com": {
                        "status": "healthy",
                        "response_time": "150ms",
                        "last_check": datetime.utcnow().isoformat()
                    },
                    "expedia": {
                        "status": "healthy",
                        "response_time": "200ms",
                        "last_check": datetime.utcnow().isoformat()
                    },
                    "airbnb": {
                        "status": "healthy",
                        "response_time": "180ms",
                        "last_check": datetime.utcnow().isoformat()
                    }
                }
            },
            "metrics": {
                "uptime": "99.9%",
                "error_rate": "0.1%",
                "average_response_time": "120ms",
                "active_integrations": 0,
                "operations_per_minute": 0
            }
        }

        return health_status

    except Exception as e:
        logger.error(f"Error fetching system health: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch system health"
        )


@router.get("/monitoring/analytics")
async def get_analytics(
    days: int = Query(7, description="Number of days to analyze"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get analytics data for the specified time period."""
    try:
        # TODO: Implement actual analytics queries
        # For now, return mock analytics data
        analytics_data = {
            "time_period": f"Last {days} days",
            "summary": {
                "total_operations": 0,
                "successful_operations": 0,
                "failed_operations": 0,
                "success_rate": "0%",
                "average_processing_time": "0s"
            },
            "daily_breakdown": [],
            "operation_types": {
                "rate_update": {"count": 0, "success_rate": "0%"},
                "availability_update": {"count": 0, "success_rate": "0%"},
                "full_sync": {"count": 0, "success_rate": "0%"}
            },
            "provider_performance": {
                "booking_com": {"operations": 0, "success_rate": "0%", "avg_response_time": "0ms"},
                "expedia": {"operations": 0, "success_rate": "0%", "avg_response_time": "0ms"},
                "airbnb": {"operations": 0, "success_rate": "0%", "avg_response_time": "0ms"}
            },
            "error_analysis": {
                "most_common_errors": [],
                "error_trends": []
            }
        }

        return analytics_data

    except Exception as e:
        logger.error(f"Error fetching analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch analytics"
        )


def _calculate_duration(start_time: Optional[datetime], end_time: Optional[datetime]) -> Optional[str]:
    """Calculate duration between two timestamps."""
    if not start_time or not end_time:
        return None

    duration = end_time - start_time
    total_seconds = duration.total_seconds()

    if total_seconds < 60:
        return f"{total_seconds:.1f}s"
    elif total_seconds < 3600:
        minutes = total_seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = total_seconds / 3600
        return f"{hours:.1f}h"
