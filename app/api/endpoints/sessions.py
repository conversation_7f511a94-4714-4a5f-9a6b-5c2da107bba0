import logging
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials

from app.core.security import get_current_user, http_bearer
from app.schemas.agent import Message, PaginatedMessages
from app.services.agent_management_service import agent_management_service
from app.services.message_service import message_service
from app.services.session_management_service import session_management_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/sessions")
async def create_session(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Create a new chat session.
    """
    try:
        user_id = current_user.get("id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User ID not found in token",
            )

        access_token = credentials.credentials if credentials else None
        session = await session_management_service.create_session(user_id=user_id, access_token=access_token)

        return {
            "session_id": session.id,
            "user_id": session.user_id,
            "created_at": session.created_at,
            "expires_at": session.expires_at,
        }
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error creating session: {str(e)}")


@router.get("/sessions/{session_id}")
async def get_session(
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Get session details
    """
    try:
        access_token = credentials.credentials if credentials else None
        session = await session_management_service.get_session(session_id, access_token=access_token)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.user_id != current_user["id"]:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this session")

        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting session: {str(e)}")


@router.get("/sessions/{session_id}/messages", response_model=PaginatedMessages)
async def get_session_messages(
    session_id: str,
    limit: int = 10,
    cursor: str = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Get messages for a session with pagination
    """
    try:
        access_token = credentials.credentials if credentials else None
        session = await session_management_service.get_session(session_id, access_token=access_token)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.user_id != current_user["id"]:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this session")

        agent_id = await agent_management_service.get_agent_id("hotel_manager", access_token)

        messages, next_cursor = await message_service.get_messages(
            agent_id=agent_id,
            session_id=session_id,
            limit=limit,
            cursor=cursor,
            access_token=access_token
        )

        return {
            "messages": [
                Message(
                    id=msg.id,
                    content=msg.content,
                    role=msg.role,
                    user_id=msg.user_id,
                    agent_id=msg.agent_id,
                    created_at=msg.created_at,
                    updated_at=msg.updated_at,
                    metadata=msg.metadata or {}
                )
                for msg in messages
            ],
            "has_more": next_cursor is not None,
            "next_cursor": next_cursor
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error getting session messages: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting session messages: {str(e)}")


@router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_session(
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Delete a session and its messages
    """
    try:
        access_token = credentials.credentials if credentials else None
        session = await session_management_service.get_session(session_id, access_token=access_token)
        if not session:
            return

        if session.user_id != current_user["id"]:
            raise HTTPException(
                status_code=403, detail="Not authorized to delete this session")

        await session_management_service.delete_session(session_id, access_token=access_token)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error deleting session: {str(e)}")
