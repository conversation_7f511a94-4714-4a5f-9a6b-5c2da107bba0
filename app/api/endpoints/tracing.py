import uuid
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
from datetime import datetime
from app.services.supabase_tracing_service import supabase_tracing_service as tracing_service
from app.core.security import get_current_user

router = APIRouter()


@router.get("/traces")
async def get_traces(
    session_id: Optional[str] = Query(
        None, description="Filter traces by session ID"),
    limit: int = Query(100, description="Maximum number of traces to return"),
    start_date: Optional[datetime] = Query(
        None, description="Start date for filtering"),
    end_date: Optional[datetime] = Query(
        None, description="End date for filtering"),
):
    """
    Retrieve tracing data with optional filtering by session_id and date range.
    If no session_id is provided, returns all available traces.
    Returns a list of trace objects with consistent structure.
    """
    print(f"[API] Getting traces. Session ID filter: {session_id}")

    try:
        # Get traces - either all or filtered by session_id
        if session_id:
            print(f"[API] Fetching traces for session: {session_id}")
            traces = tracing_service.get_traces_by_session(
                session_id, limit=limit)
        else:
            print("[API] Fetching all traces")
            traces = tracing_service.get_all_traces(limit=limit)

        print(f"[API] Retrieved {len(traces)} raw traces from service")

        if not traces:
            print("[API] No traces found in the response")
            return {"traces": []}

        # Apply date filtering if provided
        filtered_traces = []
        for trace in traces:
            try:
                # Handle different timestamp formats
                created_at = trace.get('created_at') or trace.get('createdAt')
                if created_at:
                    if isinstance(created_at, str):
                        # Handle various timestamp formats
                        try:
                            # Try standard ISO format first
                            trace_date = datetime.fromisoformat(
                                created_at.replace('Z', '+00:00'))
                        except ValueError:
                            # Handle microseconds with more than 6 digits
                            import re
                            # Truncate microseconds to 6 digits
                            fixed_timestamp = re.sub(
                                r'\.(\d{6})\d*\+', r'.\1+', created_at)
                            trace_date = datetime.fromisoformat(
                                fixed_timestamp.replace('Z', '+00:00'))
                    elif hasattr(created_at, 'isoformat'):
                        trace_date = created_at
                    else:
                        print(f"[API] Invalid createdAt format: {created_at}")
                        continue
                else:
                    trace_date = datetime.utcnow()

                if (not start_date or trace_date >= start_date) and (not end_date or trace_date <= end_date):
                    filtered_traces.append(trace)
            except Exception as e:
                print(f"[API] Error processing trace {trace.get('id')}: {e}")
                continue

        print(f"[API] After filtering, {len(filtered_traces)} traces remain")

        # Ensure we return a list of trace objects with consistent structure
        formatted_traces = []
        for trace in filtered_traces:
            try:
                # Include all fields from the trace, ensuring we have all the enhanced data
                formatted_trace = {
                    'id': str(trace.get('id', str(uuid.uuid4()))),
                    'sessionId': str(trace.get('session_id', trace.get('sessionId', ''))),
                    'createdAt': trace.get('created_at', trace.get('createdAt', datetime.utcnow().isoformat())),
                    'agent_type': trace.get('agent_type', trace.get('trace_data', {}).get('agent_type', 'unknown')),
                    'status': trace.get('status', trace.get('trace_data', {}).get('status', 'completed')),
                    'userMessage': trace.get('user_message', trace.get('userMessage')),
                    'traceData': {
                        **trace.get('trace_data', trace.get('traceData', {})),
                        'tool_calls': trace.get('tool_calls', trace.get('toolCalls')),
                        'ai_response': trace.get('ai_response', trace.get('aiResponse'))
                    },
                    'metadata': trace.get('metadata', {})
                }
                # Remove None values
                formatted_trace = {
                    k: v for k, v in formatted_trace.items() if v is not None}
                formatted_traces.append(formatted_trace)
            except Exception as e:
                print(f"[API] Error formatting trace {trace.get('id')}: {e}")
                continue

        print(f"[API] Returning {len(formatted_traces)} formatted traces")
        return {"traces": formatted_traces}
    except Exception as e:
        import traceback
        print(f"Error in get_traces: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
