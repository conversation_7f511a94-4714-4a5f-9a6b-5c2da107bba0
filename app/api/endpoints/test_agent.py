"""
Test endpoint to isolate AI agent performance without authentication or data storage.
This helps identify if the bottleneck is in the AI agent itself or in the surrounding infrastructure.
"""
import time
import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any

from app.services.agent_service import agent_service

router = APIRouter()
logger = logging.getLogger(__name__)


class TestChatRequest(BaseModel):
    message: str
    agent_type: Optional[str] = "hotel_manager"


class TestChatResponse(BaseModel):
    response: str
    response_time_ms: float
    agent_type: str
    session_id: str
    performance_metrics: Dict[str, Any]


@router.post("/test-agent-only", response_model=TestChatResponse)
async def test_agent_only(request: TestChatRequest):
    """
    Test AI agent performance without any authentication, session management, or data storage.
    This endpoint helps isolate the pure AI agent latency.
    """
    start_time = time.time()
    
    try:
        logger.info(f"Testing agent with message: {request.message}")
        
        # Minimal agent call - no auth, no session, no storage
        result = await agent_service._test_agent_direct(
            message=request.message,
            agent_type=request.agent_type
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        logger.info(f"Agent response time: {response_time_ms:.2f}ms")
        
        return TestChatResponse(
            response=result["response"],
            response_time_ms=response_time_ms,
            agent_type=result["agent_type"],
            session_id=result["session_id"],
            performance_metrics={
                "total_time_ms": response_time_ms,
                "message_length": len(request.message),
                "response_length": len(str(result["response"])),
                "agent_initialization_time": "cached" if agent_service._initialized else "first_time"
            }
        )
        
    except Exception as e:
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        logger.error(f"Error in test agent: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, 
            detail={
                "error": str(e),
                "response_time_ms": response_time_ms,
                "message": "Agent test failed"
            }
        )


@router.post("/test-agent-with-minimal-session", response_model=TestChatResponse)
async def test_agent_with_minimal_session(request: TestChatRequest):
    """
    Test AI agent with minimal session handling but no authentication or data storage.
    """
    start_time = time.time()
    
    try:
        logger.info(f"Testing agent with minimal session: {request.message}")
        
        # Generate a test session ID
        import uuid
        test_session_id = str(uuid.uuid4())
        
        # Call agent with minimal session but no storage
        result = await agent_service._test_agent_with_session(
            message=request.message,
            session_id=test_session_id,
            agent_type=request.agent_type
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        logger.info(f"Agent with session response time: {response_time_ms:.2f}ms")
        
        return TestChatResponse(
            response=result["response"],
            response_time_ms=response_time_ms,
            agent_type=result["agent_type"],
            session_id=result["session_id"],
            performance_metrics={
                "total_time_ms": response_time_ms,
                "message_length": len(request.message),
                "response_length": len(str(result["response"])),
                "session_handling": "minimal",
                "data_storage": "disabled"
            }
        )
        
    except Exception as e:
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        logger.error(f"Error in test agent with session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, 
            detail={
                "error": str(e),
                "response_time_ms": response_time_ms,
                "message": "Agent test with session failed"
            }
        )


@router.get("/test-agent-health")
async def test_agent_health():
    """
    Quick health check for the agent service.
    """
    start_time = time.time()
    
    try:
        # Initialize agent service if not already done
        if not agent_service._initialized:
            await agent_service.initialize()
        
        # Test basic agent availability
        agent = agent_service.get_agent("hotel_manager")
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        return {
            "status": "healthy",
            "agent_initialized": agent_service._initialized,
            "agent_available": agent is not None,
            "response_time_ms": response_time_ms,
            "available_agents": list(agent_service.agents.keys())
        }
        
    except Exception as e:
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        return {
            "status": "unhealthy",
            "error": str(e),
            "response_time_ms": response_time_ms
        }
