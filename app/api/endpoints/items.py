from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
from app.schemas.item import Item, ItemCreate, ItemUpdate

router = APIRouter()

# In-memory storage for demo purposes
# In a real application, this would be replaced with a database
items_db: List[Dict[str, Any]] = [
    {"id": 1, "name": "Sample Item 1", "description": "This is a sample item", "price": 10.99},
    {"id": 2, "name": "Sample Item 2", "description": "Another sample item", "price": 20.50},
]

@router.get("/", response_model=List[Item])
async def get_items():
    """Get all items"""
    return items_db

@router.get("/{item_id}", response_model=Item)
async def get_item(item_id: int):
    """Get a specific item by ID"""
    for item in items_db:
        if item["id"] == item_id:
            return item
    raise HTTPException(status_code=404, detail="Item not found")

@router.post("/", response_model=Item)
async def create_item(item: ItemCreate):
    """Create a new item"""
    new_id = max([item["id"] for item in items_db], default=0) + 1
    new_item = {"id": new_id, **item.dict()}
    items_db.append(new_item)
    return new_item

@router.put("/{item_id}", response_model=Item)
async def update_item(item_id: int, item: ItemUpdate):
    """Update an existing item"""
    for i, existing_item in enumerate(items_db):
        if existing_item["id"] == item_id:
            update_data = item.dict(exclude_unset=True)
            items_db[i].update(update_data)
            return items_db[i]
    raise HTTPException(status_code=404, detail="Item not found")

@router.delete("/{item_id}")
async def delete_item(item_id: int):
    """Delete an item"""
    for i, item in enumerate(items_db):
        if item["id"] == item_id:
            deleted_item = items_db.pop(i)
            return {"message": f"Item {item_id} deleted successfully"}
    raise HTTPException(status_code=404, detail="Item not found") 