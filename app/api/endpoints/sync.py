import logging
from typing import List, Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.core.security import get_current_user
from app.services.integration_db_service import (
    HotelIntegrationService,
    SyncOperationService,
    SyncQueueService
)
from app.schemas.integration import (
    RateUpdateRequest,
    AvailabilityUpdateRequest,
    BulkSyncRequest,
    SyncStatusResponse,
    OperationType,
    Direction,
    SyncQueueCreate
)

logger = logging.getLogger(__name__)
router = APIRouter()
http_bearer = HTTPBearer(auto_error=False)


async def queue_sync_operation(
    hotel_id: UUID,
    operation_type: OperationType,
    data: Dict[str, Any],
    providers: Optional[List[str]] = None,
    priority: int = 5
):
    """Helper function to queue sync operations."""
    queue_service = SyncQueueService(use_service_client=True)
    
    # If specific providers are requested, filter integrations
    if providers:
        data["target_providers"] = providers
    
    queue_data = SyncQueueCreate(
        hotel_id=hotel_id,
        operation_type=operation_type,
        priority=priority,
        data=data
    )
    
    return await queue_service.add_to_queue(queue_data)


@router.post("/hotels/{hotel_id}/sync/rates")
async def trigger_rate_update(
    hotel_id: UUID,
    rate_data: RateUpdateRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Trigger rate update across all or specific integrations."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        
        # Verify hotel has integrations
        integrations = await integration_service.get_hotel_integrations(hotel_id)
        if not integrations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No integrations found for this hotel"
            )
        
        # Prepare sync data
        sync_data = {
            "room_type_id": str(rate_data.room_type_id),
            "date_from": rate_data.date_from,
            "date_to": rate_data.date_to,
            "new_rate": rate_data.new_rate,
            "currency": rate_data.currency,
            "user_id": current_user["id"]
        }
        
        # Queue the sync operation
        queue_item = await queue_sync_operation(
            hotel_id=hotel_id,
            operation_type=OperationType.RATE_UPDATE,
            data=sync_data,
            providers=rate_data.providers,
            priority=3  # High priority for rate updates
        )
        
        return {
            "message": "Rate update queued successfully",
            "queue_id": queue_item.id,
            "operation_type": "rate_update",
            "status": "queued"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering rate update: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger rate update"
        )


@router.post("/hotels/{hotel_id}/sync/availability")
async def trigger_availability_update(
    hotel_id: UUID,
    availability_data: AvailabilityUpdateRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Trigger availability update across all or specific integrations."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        
        # Verify hotel has integrations
        integrations = await integration_service.get_hotel_integrations(hotel_id)
        if not integrations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No integrations found for this hotel"
            )
        
        # Prepare sync data
        sync_data = {
            "room_type_id": str(availability_data.room_type_id),
            "date": availability_data.date,
            "available_rooms": availability_data.available_rooms,
            "user_id": current_user["id"]
        }
        
        # Queue the sync operation
        queue_item = await queue_sync_operation(
            hotel_id=hotel_id,
            operation_type=OperationType.AVAILABILITY_UPDATE,
            data=sync_data,
            providers=availability_data.providers,
            priority=4  # Medium-high priority for availability updates
        )
        
        return {
            "message": "Availability update queued successfully",
            "queue_id": queue_item.id,
            "operation_type": "availability_update",
            "status": "queued"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering availability update: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger availability update"
        )


@router.post("/hotels/{hotel_id}/sync/bulk")
async def trigger_bulk_sync(
    hotel_id: UUID,
    bulk_data: BulkSyncRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Trigger bulk sync operations."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        
        # Verify hotel has integrations
        integrations = await integration_service.get_hotel_integrations(hotel_id)
        if not integrations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No integrations found for this hotel"
            )
        
        queued_operations = []
        
        # Process each operation in the bulk request
        for operation in bulk_data.operations:
            sync_data = {
                "room_type_id": str(operation.room_type_id),
                "user_id": current_user["id"]
            }
            
            # Add operation-specific data
            if operation.type == OperationType.RATE_UPDATE:
                sync_data.update({
                    "date_from": operation.date_from,
                    "date_to": operation.date_to,
                    "rate": operation.rate
                })
            elif operation.type == OperationType.AVAILABILITY_UPDATE:
                sync_data.update({
                    "date": operation.date,
                    "available_rooms": operation.available_rooms
                })
            
            # Queue the operation
            queue_item = await queue_sync_operation(
                hotel_id=hotel_id,
                operation_type=operation.type,
                data=sync_data,
                priority=5  # Normal priority for bulk operations
            )
            
            queued_operations.append({
                "queue_id": queue_item.id,
                "operation_type": operation.type.value,
                "status": "queued"
            })
        
        return {
            "message": f"Bulk sync queued successfully ({len(queued_operations)} operations)",
            "operations": queued_operations
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering bulk sync: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger bulk sync"
        )


@router.get("/hotels/{hotel_id}/sync/status")
async def get_sync_status(
    hotel_id: UUID,
    limit: int = 20,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get sync operation status for a hotel."""
    try:
        access_token = credentials.credentials if credentials else None
        sync_service = SyncOperationService(access_token=access_token)
        
        # Get recent sync operations
        operations = await sync_service.get_operations_by_hotel(hotel_id, limit=limit)
        
        return {
            "hotel_id": hotel_id,
            "operations": [
                {
                    "id": op.id,
                    "operation_type": op.operation_type,
                    "direction": op.direction,
                    "status": op.status,
                    "retry_count": op.retry_count,
                    "created_at": op.created_at,
                    "started_at": op.started_at,
                    "completed_at": op.completed_at,
                    "error_details": op.error_details
                }
                for op in operations
            ]
        }
    except Exception as e:
        logger.error(f"Error fetching sync status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch sync status"
        )


@router.post("/hotels/{hotel_id}/sync/full")
async def trigger_full_sync(
    hotel_id: UUID,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Trigger full synchronization for all integrations."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        
        # Verify hotel has integrations
        integrations = await integration_service.get_hotel_integrations(hotel_id)
        if not integrations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No integrations found for this hotel"
            )
        
        # Prepare sync data
        sync_data = {
            "sync_type": "full",
            "user_id": current_user["id"],
            "include_rates": True,
            "include_availability": True,
            "include_inventory": True
        }
        
        # Queue the full sync operation
        queue_item = await queue_sync_operation(
            hotel_id=hotel_id,
            operation_type=OperationType.FULL_SYNC,
            data=sync_data,
            priority=7  # Lower priority for full sync
        )
        
        return {
            "message": "Full sync queued successfully",
            "queue_id": queue_item.id,
            "operation_type": "full_sync",
            "status": "queued",
            "estimated_duration": "5-15 minutes"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering full sync: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger full sync"
        )
