from fastapi import APIRouter, HTTPException
from typing import Dict, Any

router = APIRouter()

@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "HM Backend API",
        "version": "1.0.0"
    }

@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check endpoint"""
    try:
        # Add any additional health checks here
        # For example: database connectivity, external service status, etc.
        
        return {
            "status": "healthy",
            "service": "HM Backend API",
            "version": "1.0.0",
            "components": {
                "api": "healthy",
                "database": "not_configured",  # Will be updated when DB is added
                "external_services": "not_configured"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}") 