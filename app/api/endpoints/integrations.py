import logging
from typing import List, Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.core.security import get_current_user
from app.services.integration_db_service import (
    IntegrationProviderService,
    HotelIntegrationService,
    SyncOperationService
)
from app.schemas.integration import (
    IntegrationProvider, IntegrationProviderCreate,
    HotelIntegration, HotelIntegrationCreate, HotelIntegrationUpdate,
    IntegrationTestResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()
http_bearer = HTTPBearer(auto_error=False)


@router.get("/providers", response_model=List[IntegrationProvider])
async def get_integration_providers(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get all available integration providers."""
    try:
        access_token = credentials.credentials if credentials else None
        provider_service = IntegrationProviderService(access_token=access_token)
        providers = await provider_service.get_all_providers()
        return providers
    except Exception as e:
        logger.error(f"Error fetching integration providers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch integration providers"
        )


@router.post("/providers", response_model=IntegrationProvider, status_code=status.HTTP_201_CREATED)
async def create_integration_provider(
    provider_data: IntegrationProviderCreate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Create a new integration provider (admin only)."""
    try:
        access_token = credentials.credentials if credentials else None
        provider_service = IntegrationProviderService(access_token=access_token)
        provider = await provider_service.create_provider(provider_data)
        return provider
    except Exception as e:
        logger.error(f"Error creating integration provider: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create integration provider"
        )


@router.get("/hotels/{hotel_id}/integrations")
async def get_hotel_integrations(
    hotel_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get all integration configurations for a hotel."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        integrations = await integration_service.get_hotel_integrations(hotel_id)
        
        # Format response to match expected structure
        formatted_integrations = []
        for integration in integrations:
            provider_data = integration.pop("provider", {})
            formatted_integrations.append({
                **integration,
                "provider": {
                    "name": provider_data.get("name"),
                    "display_name": provider_data.get("display_name")
                }
            })
        
        return {"integrations": formatted_integrations}
    except Exception as e:
        logger.error(f"Error fetching hotel integrations for {hotel_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch hotel integrations"
        )


@router.post("/hotels/{hotel_id}/integrations", response_model=HotelIntegration, status_code=status.HTTP_201_CREATED)
async def create_hotel_integration(
    hotel_id: UUID,
    integration_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Configure integration for a hotel."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        
        # Create integration configuration
        create_data = HotelIntegrationCreate(
            hotel_id=hotel_id,
            provider_id=UUID(integration_data["provider_id"]),
            credentials=integration_data.get("credentials", {}),
            configuration=integration_data.get("configuration", {}),
            is_enabled=integration_data.get("is_enabled", False)
        )
        
        integration = await integration_service.create_integration(create_data)
        return integration
    except Exception as e:
        logger.error(f"Error creating hotel integration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create hotel integration"
        )


@router.put("/hotels/{hotel_id}/integrations/{integration_id}", response_model=HotelIntegration)
async def update_hotel_integration(
    hotel_id: UUID,
    integration_id: UUID,
    update_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Update integration configuration for a hotel."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        
        # Verify integration belongs to hotel
        existing_integration = await integration_service.get_integration_by_id(integration_id)
        if not existing_integration or existing_integration.hotel_id != hotel_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found for this hotel"
            )
        
        # Update integration
        update_obj = HotelIntegrationUpdate(**update_data)
        integration = await integration_service.update_integration(integration_id, update_obj)
        return integration
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating hotel integration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update hotel integration"
        )


@router.post("/hotels/{hotel_id}/integrations/{integration_id}/test", response_model=IntegrationTestResponse)
async def test_integration_connection(
    hotel_id: UUID,
    integration_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Test integration connection."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        
        # Get integration details
        integration = await integration_service.get_integration_by_id(integration_id)
        if not integration or integration.hotel_id != hotel_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found for this hotel"
            )
        
        # TODO: Implement actual provider-specific connection testing
        # For now, return a mock response
        return IntegrationTestResponse(
            success=True,
            message="Connection test successful",
            details={
                "property_found": True,
                "api_access": True,
                "last_tested": "2024-01-01T10:00:00Z"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing integration connection: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test integration connection"
        )


@router.get("/hotels/{hotel_id}/integrations/{integration_id}/status")
async def get_integration_status(
    hotel_id: UUID,
    integration_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """Get detailed status of an integration."""
    try:
        access_token = credentials.credentials if credentials else None
        integration_service = HotelIntegrationService(access_token=access_token)
        sync_service = SyncOperationService(access_token=access_token)
        
        # Get integration details
        integration = await integration_service.get_integration_by_id(integration_id)
        if not integration or integration.hotel_id != hotel_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found for this hotel"
            )
        
        # Get recent sync operations
        recent_operations = await sync_service.get_operations_by_hotel(hotel_id, limit=10)
        
        return {
            "integration_id": integration_id,
            "sync_status": integration.sync_status,
            "last_sync_at": integration.last_sync_at,
            "error_message": integration.error_message,
            "recent_operations": [
                {
                    "id": op.id,
                    "operation_type": op.operation_type,
                    "status": op.status,
                    "created_at": op.created_at,
                    "completed_at": op.completed_at
                }
                for op in recent_operations
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching integration status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch integration status"
        )
