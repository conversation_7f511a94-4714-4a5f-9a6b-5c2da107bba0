import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel

from app.core.security import get_current_user, http_bearer
from app.schemas.agent import AgentInfo
from app.services.agent_management_service import agent_management_service

router = APIRouter()
logger = logging.getLogger(__name__)


class CreateAgentRequest(BaseModel):
    name: str
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None


@router.get("/agents", response_model=Dict[str, AgentInfo])
async def get_available_agents():
    """
    Get information about available agents
    """
    try:
        agents_info = agent_management_service.get_available_agents()
        return {
            agent_type: AgentInfo(
                name=info["name"],
                description=info["description"],
                capabilities=info["capabilities"]
            )
            for agent_type, info in agents_info.items()
        }
    except Exception as e:
        logger.error(
            f"Error getting available agents: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting available agents: {str(e)}")


@router.post("/agents", status_code=status.HTTP_201_CREATED)
async def create_agent(
    agent_data: CreateAgentRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Create a new agent
    """
    try:
        access_token = credentials.credentials if credentials else None
        agent = await agent_management_service.create_agent(
            user_id=current_user["id"],
            name=agent_data.name,
            description=agent_data.description,
            config=agent_data.config or {},
            access_token=access_token
        )
        return agent
    except Exception as e:
        logger.error(f"Error creating agent: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error creating agent: {str(e)}")


@router.get("/agents/my", response_model=List[Dict[str, Any]])
async def get_my_agents(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Get all agents for the current user
    """
    try:
        access_token = credentials.credentials if credentials else None
        return await agent_management_service.get_user_agents(current_user["id"], access_token=access_token)
    except Exception as e:
        logger.error(f"Error getting user agents: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting user agents: {str(e)}")


# LLM Being Used
# AI Agent
# Context of
