from fastapi import APIRouter
from app.api.endpoints import health, items, agents, tracing, test_agent

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(items.router, prefix="/items", tags=["items"])
api_router.include_router(agents.router, prefix="/agents", tags=["agents"])
api_router.include_router(tracing.router, tags=["tracing"])
api_router.include_router(
    test_agent.router, prefix="/test", tags=["test-agent"])
