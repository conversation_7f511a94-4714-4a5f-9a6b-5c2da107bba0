from fastapi import APIRouter
from app.api.endpoints import health, agents, tracing, chat, sessions, integrations, sync, webhooks, monitoring

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(agents.router, prefix="/agents", tags=["agents"])
api_router.include_router(
    sessions.router, prefix="/agents", tags=["sessions"])
api_router.include_router(chat.router, prefix="/agents", tags=["chat"])
api_router.include_router(tracing.router, tags=["tracing"])

# Integration system routers
api_router.include_router(
    integrations.router, prefix="/api/v1", tags=["integrations"])
api_router.include_router(sync.router, prefix="/api/v1", tags=["sync"])
api_router.include_router(webhooks.router, prefix="/api/v1", tags=["webhooks"])
api_router.include_router(
    monitoring.router, prefix="/agents", tags=["monitoring"])
