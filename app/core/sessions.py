from typing import List, Optional
import asyncio
import logging
from fastapi.encoders import jsonable_encoder
from app.schemas.agent import SessionInDB, Message
from app.services.database_service import DatabaseService

logger = logging.getLogger(__name__)


class CachedReadOnlySupabaseSession:
    """
    A read-only session that uses cached conversation history for better performance.
    This eliminates the 40+ second delay caused by fetching history on every request.
    """

    def __init__(self, session_data: SessionInDB, agent_id: str, access_token: Optional[str] = None, cached_history: Optional[List[dict]] = None):
        self.session_data = session_data
        self.agent_id = agent_id
        self.access_token = access_token
        self._cached_history = cached_history or []

    async def get_items(self) -> List[dict]:
        """Return cached conversation history without database calls."""
        logger.info(
            f"Using cached history with {len(self._cached_history)} messages for session {self.session_data.id}")
        return self._cached_history

    async def add_items(self, items: List[dict]):
        """No-op for read-only session. Items are saved in background."""
        pass

    def __getattr__(self, name):
        """Delegate attribute access to session_data."""
        return getattr(self.session_data, name)


class SupabaseSession:
    def __init__(self, session_data: SessionInDB, agent_id: str, access_token: Optional[str] = None):
        self.session_data = session_data
        self.agent_id = agent_id
        self.db_service = DatabaseService(access_token=access_token)

    async def get_items(self) -> List[dict]:
        """Fetch message history for the session."""
        if not self.session_data.id:
            return []

        messages, _ = await self.db_service.get_messages(
            agent_id=self.agent_id,
            session_id=self.session_data.id
        )

        # Return only the role and content, which is what the API expects.
        return [{"role": msg.role, "content": msg.content} for msg in messages]

    async def add_items(self, items: List[dict]):
        """Add messages to the session's history using batch operations."""
        if not items:
            return

        # Prepare batch data for single database call
        batch_messages = []
        for item in items:
            content = item.get("content")
            if content is not None:
                batch_messages.append({
                    "user_id": self.session_data.user_id,
                    "agent_id": self.agent_id,
                    "content": str(content),
                    "role": item.get("role", "system"),
                    "metadata": {},
                    "session_id": self.session_data.id,
                })

        if batch_messages:
            # Use batch insert for better performance
            await self.db_service.create_messages_batch(batch_messages)

    def __getattr__(self, name):
        """Delegate attribute access to the underlying session_data."""
        return getattr(self.session_data, name)


class ReadOnlySupabaseSession:
    """Read-only session that provides conversation history but doesn't save new messages."""

    def __init__(self, session_data: SessionInDB, agent_id: str, access_token: Optional[str] = None):
        self.session_data = session_data
        self.agent_id = agent_id
        self.db_service = DatabaseService(access_token=access_token)

    async def get_items(self) -> List[dict]:
        """Fetch message history for the session with timeout for fast response."""
        if not self.session_data.id:
            return []

        try:
            # Set a timeout for history loading to avoid blocking AI response
            messages, _ = await asyncio.wait_for(
                self.db_service.get_messages(
                    agent_id=self.agent_id,
                    session_id=self.session_data.id,
                    limit=20  # Limit to recent messages for faster loading
                ),
                timeout=1.0  # 1 second timeout
            )

            # Return only the role and content, which is what the API expects.
            return [{"role": msg.role, "content": msg.content} for msg in messages]
        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout loading history for session {self.session_data.id}, proceeding without history")
            return []
        except Exception as e:
            logger.warning(
                f"Error loading history for session {self.session_data.id}: {e}, proceeding without history")
            return []

    async def add_items(self, items: List[dict]):
        """No-op: Don't save messages during agent processing for faster response."""
        pass

    def __getattr__(self, name):
        """Delegate attribute access to the underlying session_data."""
        return getattr(self.session_data, name)
