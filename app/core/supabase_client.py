import os
from typing import Optional
from supabase import create_client, Client as Supabase<PERSON>lient
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class SupabaseManager:
    _instance: Optional[SupabaseClient] = None
    _service_client: Optional[SupabaseClient] = None
    
    @classmethod
    def get_client(cls) -> SupabaseClient:
        """Get or create a Supabase client instance using the anonymous key.
        
        Returns:
            SupabaseClient: Initialized Supabase client
            
        Raises:
            ValueError: If required environment variables are not set
        """
        if cls._instance is None:
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_ANON_KEY")
            
            if not supabase_url or not supabase_key:
                error_msg = "SUPABASE_URL and SUPABASE_ANON_KEY must be set in environment variables"
                logger.error(error_msg)
                raise ValueError(error_msg)
                
            logger.info("Initializing Supabase client with anonymous key")
            cls._instance = create_client(supabase_url, supabase_key)
            
        return cls._instance
        
    @classmethod
    def get_service_client(cls) -> SupabaseClient:
        """Get or create a Supabase client instance using the service role key.
        
        This client should be used for administrative tasks that require bypassing RLS.
        
        Returns:
            SupabaseClient: Initialized Supabase client with service role privileges
            
        Raises:
            ValueError: If required environment variables are not set
        """
        if cls._service_client is None:
            supabase_url = os.getenv("SUPABASE_URL")
            service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            
            if not supabase_url or not service_key:
                error_msg = "SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set for the service client"
                logger.error(error_msg)
                raise ValueError(error_msg)
                
            logger.info("Initializing Supabase client with service role key")
            cls._service_client = create_client(supabase_url, service_key)
            
        return cls._service_client

# Initialize Supabase clients
supabase = SupabaseManager.get_client()
supabase_service = SupabaseManager.get_service_client()

# Test the connection
async def test_connection() -> bool:
    """Test the Supabase connection.
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        client = SupabaseManager.get_client()
        # Try a simple query to test the connection
        result = client.table('agents').select("count", count='exact').execute()
        logger.info("Successfully connected to Supabase")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Supabase: {str(e)}")
        return False
