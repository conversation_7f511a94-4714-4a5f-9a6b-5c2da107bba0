from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import List, Optional, Union, Dict, Any
from functools import lru_cache
import os
from pathlib import Path


class Settings(BaseSettings):
    # Application Configuration
    PROJECT_NAME: str = "HM Backend API"
    ENVIRONMENT: str = "development"
    DEBUG: bool = False

    # API Configuration
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days

    # CORS Configuration
    CORS_ORIGINS: List[str] = ["http://localhost:3000",
                               "http://localhost:8000", "https://hm-one-xi.vercel.app/"]

    # Supabase Configuration
    SUPABASE_URL: str
    SUPABASE_ANON_KEY: str
    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = None

    # Azure OpenAI Configuration
    AZURE_OPENAI_API_KEY: Optional[str] = None
    AZURE_OPENAI_ENDPOINT: Optional[str] = None
    AZURE_OPENAI_DEPLOYMENT: Optional[str] = None
    AZURE_OPENAI_MODEL: Optional[str] = "gpt-4"
    AZURE_OPENAI_API_VERSION: str = "2025-03-01-preview"

    # AWS Configuration (for any AWS services still in use)
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"

    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Security
    SECURITY_ALGORITHM: str = "HS256"
    # Enable/disable safety checks for agent inputs
    SAFETY_CHECKS_ENABLED: bool = True
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str
    SUPABASE_JWT_SECRET: str

    # Retry Configuration
    DEFAULT_MAX_RETRIES: int = 3  # Default number of retries for agent operations
    DEFAULT_RETRY_DELAY: float = 1.0  # Default delay between retries in seconds

    # Pagination
    DEFAULT_PAGE_SIZE: int = 10
    MAX_PAGE_SIZE: int = 100

    # File Uploads
    UPLOAD_DIR: str = "uploads"

    # DynamoDB Configuration
    # Default table name for agent tracing
    DYNAMODB_TABLE_NAME: str = "agent_traces"
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB

    # Session Configuration
    SESSION_EXPIRE_HOURS: int = 24 * 7  # 1 week

    # System User ID
    SYSTEM_USER_ID: str = "00000000-0000-0000-0000-000000000000"  # Default system user ID

    @property
    def is_production(self) -> bool:
        return self.ENVIRONMENT == "production"

    @property
    def is_development(self) -> bool:
        return self.ENVIRONMENT == "development"

    @property
    def is_testing(self) -> bool:
        return self.ENVIRONMENT == "testing"

    # Use Pydantic v2 style config
    model_config = SettingsConfigDict(
        env_file=os.path.join(os.path.dirname(__file__), "..", "..", ".env"),
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore",
    )


# Create settings instance
settings = Settings()

# Create upload directory if it doesn't exist
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance.

    This function is used as a dependency in FastAPI routes to inject settings.
    """
    return settings
