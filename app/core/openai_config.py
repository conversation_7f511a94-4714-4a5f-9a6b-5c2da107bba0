import os
import logging
from openai import AsyncAzureOpenAI
from agents import set_default_openai_client
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate required Azure OpenAI settings
required_settings = [
    ("AZURE_OPENAI_API_KEY", settings.AZURE_OPENAI_API_KEY),
    ("AZURE_OPENAI_ENDPOINT", settings.AZURE_OPENAI_ENDPOINT),
    ("AZURE_OPENAI_DEPLOYMENT", settings.AZURE_OPENAI_DEPLOYMENT),
    ("AZURE_OPENAI_API_VERSION", settings.AZURE_OPENAI_API_VERSION)
]

missing_settings = [name for name, value in required_settings if not value]
if missing_settings:
    error_msg = f"Missing required Azure OpenAI settings: {', '.join(missing_settings)}"
    logger.error(error_msg)
    raise ValueError(error_msg)

# Set environment variables for Azure OpenAI
os.environ["OPENAI_API_TYPE"] = "azure"
os.environ["OPENAI_API_VERSION"] = settings.AZURE_OPENAI_API_VERSION
os.environ["OPENAI_API_BASE"] = settings.AZURE_OPENAI_ENDPOINT
os.environ["OPENAI_API_KEY"] = settings.AZURE_OPENAI_API_KEY

# Log Azure OpenAI configuration
logger.info("🔧 Azure OpenAI Configuration:")
logger.info(f"- Endpoint: {settings.AZURE_OPENAI_ENDPOINT}")
logger.info(f"- API Version: {settings.AZURE_OPENAI_API_VERSION}")
logger.info(f"- Deployment: {settings.AZURE_OPENAI_DEPLOYMENT}")
logger.info(f"- Model: {getattr(settings, 'AZURE_OPENAI_MODEL', 'Not set')}")

class AzureOpenAIClient:
    _instance = None
    _client = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AzureOpenAIClient, cls).__new__(cls)
        return cls._instance
    
    async def initialize(self):
        if not self._initialized:
            try:
                # Create the client
                self._client = AsyncAzureOpenAI(
                    api_key=settings.AZURE_OPENAI_API_KEY,
                    api_version=settings.AZURE_OPENAI_API_VERSION,
                    azure_endpoint=settings.AZURE_OPENAI_ENDPOINT.rstrip('/'),
                    azure_deployment=settings.AZURE_OPENAI_DEPLOYMENT,
                )
                
                # Test the connection
                await self.test_connection()
                
                # Set as the default OpenAI client for the Agents SDK
                set_default_openai_client(self._client)
                logger.info("✅ Azure OpenAI client configured as default")
                self._initialized = True
                
            except Exception as e:
                error_msg = f"Failed to initialize Azure OpenAI client: {str(e)}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
    
    async def test_connection(self):
        try:
            # For Azure OpenAI, we use max_completion_tokens instead of max_tokens
            await self._client.chat.completions.create(
                model=settings.AZURE_OPENAI_DEPLOYMENT,
                messages=[{"role": "system", "content": "You are a helpful assistant."}],
                max_completion_tokens=5
            )
            logger.info("✅ Successfully connected to Azure OpenAI")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Azure OpenAI: {str(e)}")
            raise
    
    @property
    def client(self):
        if not self._initialized:
            raise RuntimeError("Azure OpenAI client not initialized. Call initialize() first.")
        return self._client

# Create a singleton instance of the client
azure_openai_client = AzureOpenAIClient()
