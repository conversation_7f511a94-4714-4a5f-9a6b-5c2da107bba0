from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Op<PERSON>, Dict, Any, <PERSON>, <PERSON><PERSON>
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from app.core.config import settings
import httpx
from jose import jwk
from jose.utils import base64url_decode
import json

# Security configuration
SECRET_KEY = settings.JWT_SECRET_KEY
ALGORITHM = settings.JWT_ALGORITHM
ACCESS_TOKEN_EXPIRE_MINUTES = settings.ACCESS_TOKEN_EXPIRE_MINUTES

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Security schemes
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
http_bearer = HTTPBearer(auto_error=False)

class TokenData(BaseModel):
    username: Optional[str] = None
    user_id: Optional[str] = None
    sub: Optional[str] = None  # Supabase user ID
    email: Optional[str] = None
    role: Optional[str] = None
    is_supabase: bool = False

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate a password hash"""
    return pwd_context.hash(password)

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def verify_supabase_jwt(token: str) -> Dict[str, Any]:
    """Verify a Supabase JWT token"""
    print("Verifying Supabase JWT using secret...")
    try:
        payload = jwt.decode(
            token,
            settings.SUPABASE_JWT_SECRET,
            algorithms=["HS256"],
            audience='authenticated',
            issuer=f"{settings.SUPABASE_URL}/auth/v1",
            options={"verify_aud": True, "verify_iss": True}
        )
        
        return {
            'sub': payload.get('sub'),
            'email': payload.get('email'),
            'role': payload.get('role'),
            'is_supabase': True
        }
        
    except Exception as e:
        raise JWTError(f'Error verifying Supabase token: {str(e)}')

async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
) -> Dict[str, Any]:
    """
    Get the current user from either a local JWT or Supabase JWT.
    
    Args:
        credentials: The HTTP Authorization header credentials
        
    Returns:
        Dict containing user information
        
    Raises:
        HTTPException: If the token is invalid or expired
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = credentials.credentials
    
    # Try to verify as Supabase token first
    try:
        supabase_user = await verify_supabase_jwt(token)
        return {
            'id': supabase_user['sub'],
            'email': supabase_user['email'],
            'is_active': True,
            'is_superuser': supabase_user.get('role') == 'service_role',
            'is_supabase': True
        }
    except JWTError as e:
        # If Supabase verification fails, raise the specific error
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Supabase token error: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_active_user(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Get the current active user.
    
    Args:
        current_user: The user object from get_current_user
        
    Returns:
        The active user
        
    Raises:
        HTTPException: If the user is inactive
    """
    if not current_user or not current_user.get("is_active"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return current_user

async def get_admin_user(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Get the current admin user.
    
    Args:
        current_user: The user object from get_current_user
        
    Returns:
        The admin user
        
    Raises:
        HTTPException: If the user is not an admin
    """
    if not current_user or not current_user.get("is_superuser"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return current_user

# For testing purposes
def create_test_token(user_id: str, is_supabase: bool = False):
    """
    Create a test JWT token for the given user ID
    
    Args:
        user_id: The user ID to include in the token
        is_supabase: Whether to create a Supabase-style token
        
    Returns:
        A JWT token string
    """
    if is_supabase:
        # Create a Supabase-style token
        expires = int((datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)).timestamp())
        payload = {
            "sub": user_id,
            "email": f"{user_id}@example.com",
            "role": "authenticated",
            "exp": expires,
            "aud": "authenticated",
            "iss": settings.SUPABASE_URL,
        }
        return jwt.encode(payload, settings.SUPABASE_JWT_SECRET, algorithm="HS256")
    else:
        # Create a standard JWT token
        data = {"sub": user_id}
        expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        return create_access_token(data=data, expires_delta=expires)
