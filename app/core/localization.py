import json
from pathlib import Path
from typing import Dict, Any, Optional
import logging
from langdetect import detect, LangDetectException

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LocalizationService:
    """
    Service for handling language detection and loading translations.
    """
    
    def __init__(self, locales_dir: Path, default_lang: str = "en"):
        self.locales_dir = locales_dir
        self.default_lang = default_lang
        self.translations: Dict[str, Dict[str, Any]] = {}
        self._load_translations()
        
    def _load_translations(self):
        """Load all available translation files from the locales directory."""
        if not self.locales_dir.is_dir():
            logger.warning(f"Locales directory not found: {self.locales_dir}")
            return
            
        for lang_file in self.locales_dir.glob("*.json"):
            lang = lang_file.stem
            try:
                with open(lang_file, "r", encoding="utf-8") as f:
                    self.translations[lang] = json.load(f)
                logger.info(f"Loaded translations for language: {lang}")
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"Failed to load translation file {lang_file}: {e}")
                
    def detect_language(self, text: str) -> str:
        """
        Detect the language of a given text.
        
        Args:
            text: The text to analyze.
            
        Returns:
            The detected language code (e.g., "en", "de", "it").
            
        Raises:
            ValueError: If the language is not supported or detection fails.
        """
        if not text:
            raise ValueError("Input text is empty.")
            
        try:
            lang = detect(text)
            if lang not in self.translations:
                raise ValueError(f"Unsupported language: {lang}")
            return lang
        except LangDetectException:
            raise ValueError("Language detection failed.")
            
    def get_translation(self, lang: str, key: str, default: Optional[Any] = None) -> Any:
        """
        Get a translation for a specific language and key.
        
        Args:
            lang: The language code.
            key: The key for the translation string.
            default: The default value to return if the key is not found.
            
        Returns:
            The translated string or the default value.
        """
        return self.translations.get(lang, {}).get(key, default)

# Create a global instance of the localization service
locales_path = Path(__file__).parent.parent / "locales"
localization_service = LocalizationService(locales_dir=locales_path)
