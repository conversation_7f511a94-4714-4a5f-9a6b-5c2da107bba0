import logging
from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime

from fastapi import HTTP<PERSON>x<PERSON>, status
from supabase import Client

from app.core.supabase_client import supabase, supabase_service
from app.schemas.integration import (
    IntegrationProvider, IntegrationProviderCreate,
    HotelIntegration, HotelIntegrationCreate, HotelIntegrationUpdate,
    SyncOperation, SyncOperationCreate,
    SyncQueueItem, SyncQueueCreate,
    SyncStatus, OperationStatus
)

logger = logging.getLogger(__name__)


class IntegrationProviderService:
    """Service for managing integration providers."""

    PROVIDERS_TABLE = "integration_providers"

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)

    async def get_all_providers(self) -> List[IntegrationProvider]:
        """Get all integration providers."""
        try:
            response = self.supabase.table(
                self.PROVIDERS_TABLE).select("*").execute()
            return [IntegrationProvider(**provider) for provider in response.data]
        except Exception as e:
            logger.error(f"Error fetching providers: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch integration providers"
            )

    async def get_provider_by_id(self, provider_id: UUID) -> Optional[IntegrationProvider]:
        """Get provider by ID."""
        try:
            response = self.supabase.table(self.PROVIDERS_TABLE).select(
                "*").eq("id", str(provider_id)).execute()
            if response.data:
                return IntegrationProvider(**response.data[0])
            return None
        except Exception as e:
            logger.error(f"Error fetching provider {provider_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch integration provider"
            )

    async def get_provider_by_name(self, name: str) -> Optional[IntegrationProvider]:
        """Get provider by name."""
        try:
            response = self.supabase.table(self.PROVIDERS_TABLE).select(
                "*").eq("name", name).execute()
            if response.data:
                return IntegrationProvider(**response.data[0])
            return None
        except Exception as e:
            logger.error(f"Error fetching provider {name}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch integration provider"
            )

    async def create_provider(self, provider_data: IntegrationProviderCreate) -> IntegrationProvider:
        """Create a new integration provider."""
        try:
            data = provider_data.model_dump()
            response = self.supabase.table(
                self.PROVIDERS_TABLE).insert(data).execute()
            if response.data:
                return IntegrationProvider(**response.data[0])
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create integration provider"
            )
        except Exception as e:
            logger.error(f"Error creating provider: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create integration provider"
            )


class HotelIntegrationService:
    """Service for managing hotel integrations."""

    INTEGRATIONS_TABLE = "hotel_integrations"

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)

    async def get_hotel_integrations(self, hotel_id: UUID) -> List[Dict[str, Any]]:
        """Get all integrations for a hotel with provider details."""
        try:
            response = self.supabase.table(self.INTEGRATIONS_TABLE).select(
                "*, integration_providers(*)"
            ).eq("hotel_id", str(hotel_id)).execute()

            integrations = []
            for item in response.data:
                provider_data = item.pop("integration_providers")
                integration = HotelIntegration(**item)
                integrations.append({
                    **integration.model_dump(),
                    "provider": provider_data
                })
            return integrations
        except Exception as e:
            logger.error(
                f"Error fetching hotel integrations for {hotel_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch hotel integrations"
            )

    async def get_integration_by_id(self, integration_id: UUID) -> Optional[HotelIntegration]:
        """Get integration by ID."""
        try:
            response = self.supabase.table(self.INTEGRATIONS_TABLE).select(
                "*").eq("id", str(integration_id)).execute()
            if response.data:
                return HotelIntegration(**response.data[0])
            return None
        except Exception as e:
            logger.error(f"Error fetching integration {integration_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch integration"
            )

    async def create_integration(self, integration_data: HotelIntegrationCreate) -> HotelIntegration:
        """Create a new hotel integration."""
        try:
            data = integration_data.model_dump()
            data["hotel_id"] = str(data["hotel_id"])
            data["provider_id"] = str(data["provider_id"])

            response = self.supabase.table(
                self.INTEGRATIONS_TABLE).insert(data).execute()
            if response.data:
                return HotelIntegration(**response.data[0])
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create integration"
            )
        except Exception as e:
            logger.error(f"Error creating integration: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create integration"
            )

    async def update_integration(self, integration_id: UUID, update_data: HotelIntegrationUpdate) -> HotelIntegration:
        """Update an existing hotel integration."""
        try:
            data = update_data.model_dump(exclude_unset=True)
            data["updated_at"] = datetime.utcnow().isoformat()

            response = self.supabase.table(self.INTEGRATIONS_TABLE).update(
                data).eq("id", str(integration_id)).execute()
            if response.data:
                return HotelIntegration(**response.data[0])
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found"
            )
        except Exception as e:
            logger.error(f"Error updating integration {integration_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update integration"
            )

    async def update_sync_status(self, integration_id: UUID, sync_status: SyncStatus, error_message: Optional[str] = None):
        """Update sync status for an integration."""
        try:
            data = {
                "sync_status": sync_status.value,
                "last_sync_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            if error_message:
                data["error_message"] = error_message

            response = self.supabase.table(self.INTEGRATIONS_TABLE).update(
                data).eq("id", str(integration_id)).execute()
            if not response.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Integration not found"
                )
        except Exception as e:
            logger.error(
                f"Error updating sync status for {integration_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update sync status"
            )


class SyncOperationService:
    """Service for managing sync operations."""

    OPERATIONS_TABLE = "sync_operations"

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)

    async def create_operation(self, operation_data: SyncOperationCreate) -> SyncOperation:
        """Create a new sync operation."""
        try:
            data = operation_data.model_dump()
            data["hotel_integration_id"] = str(data["hotel_integration_id"])
            if data.get("scheduled_at"):
                data["scheduled_at"] = data["scheduled_at"].isoformat()

            response = self.supabase.table(
                self.OPERATIONS_TABLE).insert(data).execute()
            if response.data:
                return SyncOperation(**response.data[0])
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create sync operation"
            )
        except Exception as e:
            logger.error(f"Error creating sync operation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create sync operation"
            )

    async def update_operation_status(self, operation_id: UUID, status: OperationStatus, error_details: Optional[Dict[str, Any]] = None):
        """Update operation status."""
        try:
            data = {
                "status": status.value,
                "updated_at": datetime.utcnow().isoformat()
            }

            if status == OperationStatus.PROCESSING:
                data["started_at"] = datetime.utcnow().isoformat()
            elif status in [OperationStatus.SUCCESS, OperationStatus.FAILED]:
                data["completed_at"] = datetime.utcnow().isoformat()

            if error_details:
                data["error_details"] = error_details

            response = self.supabase.table(self.OPERATIONS_TABLE).update(
                data).eq("id", str(operation_id)).execute()
            if not response.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Operation not found"
                )
        except Exception as e:
            logger.error(
                f"Error updating operation status for {operation_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update operation status"
            )

    async def get_operations_by_hotel(self, hotel_id: UUID, limit: int = 50) -> List[SyncOperation]:
        """Get recent sync operations for a hotel."""
        try:
            response = self.supabase.table(self.OPERATIONS_TABLE).select(
                "*, hotel_integrations!inner(hotel_id)"
            ).eq("hotel_integrations.hotel_id", str(hotel_id)).order("created_at", desc=True).limit(limit).execute()

            return [SyncOperation(**op) for op in response.data]
        except Exception as e:
            logger.error(
                f"Error fetching operations for hotel {hotel_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch sync operations"
            )


class SyncQueueService:
    """Service for managing sync queue."""

    QUEUE_TABLE = "sync_queue"

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)

    async def add_to_queue(self, queue_data: SyncQueueCreate) -> SyncQueueItem:
        """Add item to sync queue."""
        try:
            data = queue_data.model_dump()
            data["hotel_id"] = str(data["hotel_id"])
            if not data.get("scheduled_for"):
                data["scheduled_for"] = datetime.utcnow().isoformat()

            response = self.supabase.table(
                self.QUEUE_TABLE).insert(data).execute()
            if response.data:
                return SyncQueueItem(**response.data[0])
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add item to sync queue"
            )
        except Exception as e:
            logger.error(f"Error adding to sync queue: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add item to sync queue"
            )

    async def get_pending_items(self, limit: int = 10) -> List[SyncQueueItem]:
        """Get pending items from sync queue ordered by priority and scheduled time."""
        try:
            response = self.supabase.table(self.QUEUE_TABLE).select("*").eq(
                "status", OperationStatus.PENDING.value
            ).lte("scheduled_for", datetime.utcnow().isoformat()).order(
                "priority", desc=False
            ).order("scheduled_for", desc=False).limit(limit).execute()

            return [SyncQueueItem(**item) for item in response.data]
        except Exception as e:
            logger.error(f"Error fetching pending queue items: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch pending queue items"
            )

    async def update_queue_item_status(self, item_id: UUID, status: OperationStatus, increment_attempts: bool = False):
        """Update queue item status."""
        try:
            data = {"status": status.value}
            if increment_attempts:
                # First get current attempts count
                current = self.supabase.table(self.QUEUE_TABLE).select(
                    "attempts").eq("id", str(item_id)).execute()
                if current.data:
                    data["attempts"] = current.data[0]["attempts"] + 1

            response = self.supabase.table(self.QUEUE_TABLE).update(
                data).eq("id", str(item_id)).execute()
            if not response.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Queue item not found"
                )
        except Exception as e:
            logger.error(
                f"Error updating queue item status for {item_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update queue item status"
            )

    async def remove_from_queue(self, item_id: UUID):
        """Remove item from sync queue."""
        try:
            response = self.supabase.table(self.QUEUE_TABLE).delete().eq(
                "id", str(item_id)).execute()
            if not response.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Queue item not found"
                )
        except Exception as e:
            logger.error(f"Error removing queue item {item_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to remove queue item"
            )
