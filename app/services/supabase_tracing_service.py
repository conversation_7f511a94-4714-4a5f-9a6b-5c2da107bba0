"""
Supabase-based tracing service for storing agent execution traces.
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import uuid4

from app.core.supabase_client import supabase_service
from app.core.config import settings

logger = logging.getLogger(__name__)


class SupabaseTracingService:
    """Service for handling tracing of agent executions and storing in Supabase"""
    
    def __init__(self):
        self.supabase = supabase_service
        self.table_name = 'traces'
    
    def store_trace(self, trace_data: Dict[str, Any], session_id: str, user_id: Optional[str] = None) -> str:
        """Store a trace in Supabase with enhanced structure for tool calls and AI responses"""
        trace_id = str(uuid4())
        timestamp = datetime.utcnow()
        
        try:
            # Extract specific fields from trace_data
            user_message = trace_data.pop('message', trace_data.pop('input', None))
            ai_response = trace_data.get('ai_response') or trace_data.get('output')
            tool_calls = trace_data.get('tool_calls')
            agent_type = trace_data.get('agent_type', trace_data.get('agent_id', 'unknown'))
            status = trace_data.get('status', 'completed')
            duration_seconds = trace_data.get('duration_seconds')
            start_time = trace_data.get('start_time')
            end_time = trace_data.get('end_time')
            
            # Convert string timestamps to datetime objects if needed
            if isinstance(start_time, str):
                try:
                    start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                except:
                    start_time = None
            
            if isinstance(end_time, str):
                try:
                    end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                except:
                    end_time = None
            
            # Prepare the trace record
            trace_record = {
                'id': trace_id,
                'session_id': session_id,
                'user_id': user_id,
                'agent_type': str(agent_type),
                'status': str(status),
                'user_message': str(user_message) if user_message else None,
                'ai_response': ai_response,
                'tool_calls': tool_calls,
                'trace_data': trace_data.copy(),  # Store the full trace data as JSONB
                'metadata': {
                    'timestamp': timestamp.isoformat(),
                    'safety_checks': trace_data.get('safety_checks'),
                    'success': trace_data.get('success', True)
                },
                'duration_seconds': float(duration_seconds) if duration_seconds else None,
                'start_time': start_time.isoformat() if start_time else None,
                'end_time': end_time.isoformat() if end_time else None,
                'created_at': timestamp.isoformat(),
                'updated_at': timestamp.isoformat()
            }
            
            # Store in Supabase
            result = self.supabase.table(self.table_name).insert(trace_record).execute()
            
            if result.data:
                logger.debug(f"Successfully stored trace {trace_id} in Supabase")
                return trace_id
            else:
                logger.error(f"Failed to store trace in Supabase: No data returned")
                return ""
            
        except Exception as e:
            logger.error(f"Error storing trace in Supabase: {e}", exc_info=True)
            return ""

    async def store_trace_async(self, trace_data: Dict[str, Any], session_id: str, user_id: Optional[str] = None) -> str:
        """Asynchronously store a trace in Supabase."""
        return await asyncio.to_thread(self.store_trace, trace_data, session_id, user_id)
    
    def get_traces_by_session(self, session_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get all traces for a specific session"""
        try:
            result = self.supabase.table(self.table_name)\
                .select('*')\
                .eq('session_id', session_id)\
                .order('created_at', desc=True)\
                .limit(limit)\
                .execute()
            
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Error getting traces for session {session_id}: {e}")
            return []
    
    def get_traces_by_user(self, user_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get all traces for a specific user"""
        try:
            result = self.supabase.table(self.table_name)\
                .select('*')\
                .eq('user_id', user_id)\
                .order('created_at', desc=True)\
                .limit(limit)\
                .execute()
            
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Error getting traces for user {user_id}: {e}")
            return []
    
    def get_all_traces(self, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """Get all traces with pagination"""
        try:
            result = self.supabase.table(self.table_name)\
                .select('*')\
                .order('created_at', desc=True)\
                .range(offset, offset + limit - 1)\
                .execute()
            
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Error getting all traces: {e}")
            return []
    
    def record_error(self, error_data: Dict[str, Any]) -> str:
        """Record an error in the tracing system"""
        try:
            error_id = str(uuid4())
            timestamp = datetime.utcnow()
            
            trace_record = {
                'id': error_id,
                'session_id': error_data.get('session_id', 'unknown'),
                'user_id': error_data.get('user_id'),
                'agent_type': 'error',
                'status': 'error',
                'user_message': error_data.get('message'),
                'trace_data': error_data,
                'metadata': {
                    'timestamp': timestamp.isoformat(),
                    'error_type': error_data.get('error_type'),
                    'error_message': error_data.get('error_message')
                },
                'created_at': timestamp.isoformat(),
                'updated_at': timestamp.isoformat()
            }
            
            result = self.supabase.table(self.table_name).insert(trace_record).execute()
            
            if result.data:
                return error_id
            else:
                logger.error("Failed to record error in Supabase")
                return ""
                
        except Exception as e:
            logger.error(f"Error recording error in tracing system: {e}")
            return ""


# Global instance
supabase_tracing_service = SupabaseTracingService()
