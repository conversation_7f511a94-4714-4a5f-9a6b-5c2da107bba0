import logging
from typing import List, Dict, Any, Optional, Tuple

from fastapi import HTTPEx<PERSON>, status
from supabase import Client

from app.core.supabase_client import supabase, supabase_service
from app.schemas.agent import Message

logger = logging.getLogger(__name__)


class MessageDBService:
    """Service for handling message-related database operations."""

    MESSAGES_TABLE: str = 'messages'

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)

    async def create_message(self, user_id: str, agent_id: str, content: str, role: str, metadata: dict, session_id: Optional[str] = None) -> Message:
        """Create a new message."""
        try:
            message_dict = {
                "user_id": user_id,
                "agent_id": agent_id,
                "content": content,
                "role": role,
                "metadata": metadata,
                "session_id": session_id,
            }
            result = self.supabase.table(self.MESSAGES_TABLE).insert(
                message_dict).execute()
            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create message"
                )
            return Message(**result.data[0])
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating message: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating the message"
            )

    async def create_messages_batch(self, messages_data: List[Dict[str, Any]]) -> List[Message]:
        """Create multiple messages in a single batch operation."""
        if not messages_data:
            return []
        try:
            result = self.supabase.table(self.MESSAGES_TABLE).insert(
                messages_data).execute()
            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create batch messages"
                )
            return [Message(**msg) for msg in result.data]
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Error creating batch messages: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating batch messages"
            )

    async def get_messages(
        self,
        agent_id: str,
        session_id: Optional[str] = None,
        limit: int = 10,
        cursor: Optional[str] = None
    ) -> Tuple[List[Message], Optional[str]]:
        """Get messages for an agent with pagination."""
        limit = max(1, min(limit, 100))
        try:
            query = self.supabase.table(self.MESSAGES_TABLE).select(
                '*').eq('agent_id', agent_id)
            if session_id:
                query = query.eq('session_id', session_id)
            query = query.order('created_at', desc=True).limit(limit + 1)
            if cursor:
                query = query.lt('created_at', cursor)
            result = query.execute()
            messages_data = result.data or []
            has_more = len(messages_data) > limit
            if has_more:
                messages_data = messages_data[:-1]
                next_cursor = messages_data[-1]['created_at']
            else:
                next_cursor = None
            messages = [Message(**msg) for msg in messages_data]
            return messages, next_cursor
        except Exception as e:
            logger.error(
                f"Error getting messages for agent {agent_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving messages"
            )

    async def delete_messages_by_session(self, session_id: str) -> bool:
        """Delete all messages for a session."""
        try:
            result = self.supabase.table(self.MESSAGES_TABLE).delete().eq(
                'session_id', session_id).execute()
            return bool(result.data)
        except Exception as e:
            logger.error(
                f"Error deleting messages for session {session_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while deleting messages"
            )
