import logging
from datetime import datetime, timezone
from typing import Optional

from fastapi import HTT<PERSON>Ex<PERSON>, status
from supabase import Client

from app.core.supabase_client import supabase, supabase_service
from app.schemas.agent import Session, SessionCreate

logger = logging.getLogger(__name__)


class SessionDBService:
    """Service for handling session-related database operations."""

    SESSIONS_TABLE: str = 'sessions'

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)

    async def create_session(self, session_data: SessionCreate) -> Session:
        """Create a new user session."""
        try:
            session_dict = session_data.dict()
            if isinstance(session_dict.get('expires_at'), datetime):
                session_dict['expires_at'] = session_dict['expires_at'].isoformat()
            result = self.supabase.table(self.SESSIONS_TABLE).insert(
                session_dict).execute()
            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create session"
                )
            return Session(**result.data[0])
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating the session"
            )

    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        try:
            result = self.supabase.table(self.SESSIONS_TABLE).select('*').eq('id', session_id).gt(
                'expires_at', datetime.now(timezone.utc).isoformat()).execute()
            if not result.data:
                return None
            return Session(**result.data[0])
        except Exception as e:
            logger.warning(
                f"Could not get session {session_id}: {str(e)}")
            return None

    async def delete_session(self, session_id: str) -> bool:
        """Delete a session."""
        try:
            result = self.supabase.table(self.SESSIONS_TABLE).delete().eq(
                'id', session_id).execute()
            return bool(result.data)
        except Exception as e:
            logger.error(
                f"Error deleting session {session_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while deleting the session"
            )
