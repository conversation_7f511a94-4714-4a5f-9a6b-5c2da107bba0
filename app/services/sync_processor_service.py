import asyncio
import logging
from typing import Dict, Any, Optional, List
from uuid import UUID
from datetime import datetime, timedelta

from app.services.integration_db_service import (
    SyncQueueService,
    SyncOperationService,
    HotelIntegrationService
)
from app.schemas.integration import (
    OperationType,
    OperationStatus,
    Direction,
    SyncOperationCreate
)

logger = logging.getLogger(__name__)


class SyncProcessorService:
    """Service for processing sync operations in the background."""
    
    def __init__(self):
        self.queue_service = SyncQueueService(use_service_client=True)
        self.operation_service = SyncOperationService(use_service_client=True)
        self.integration_service = HotelIntegrationService(use_service_client=True)
        self.is_running = False
        self.max_concurrent_operations = 5
        self.processing_semaphore = asyncio.Semaphore(self.max_concurrent_operations)
    
    async def start_processor(self):
        """Start the background sync processor."""
        if self.is_running:
            logger.warning("Sync processor is already running")
            return
        
        self.is_running = True
        logger.info("Starting sync processor...")
        
        while self.is_running:
            try:
                await self._process_queue_batch()
                await asyncio.sleep(5)  # Check queue every 5 seconds
            except Exception as e:
                logger.error(f"Error in sync processor main loop: {e}")
                await asyncio.sleep(10)  # Wait longer on error
    
    async def stop_processor(self):
        """Stop the background sync processor."""
        logger.info("Stopping sync processor...")
        self.is_running = False
    
    async def _process_queue_batch(self):
        """Process a batch of pending queue items."""
        try:
            # Get pending items from queue
            pending_items = await self.queue_service.get_pending_items(limit=10)
            
            if not pending_items:
                return
            
            logger.info(f"Processing {len(pending_items)} sync operations")
            
            # Process items concurrently with semaphore limit
            tasks = []
            for item in pending_items:
                task = asyncio.create_task(self._process_queue_item(item))
                tasks.append(task)
            
            # Wait for all tasks to complete
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"Error processing queue batch: {e}")
    
    async def _process_queue_item(self, queue_item):
        """Process a single queue item."""
        async with self.processing_semaphore:
            try:
                # Update queue item status to processing
                await self.queue_service.update_queue_item_status(
                    queue_item.id, 
                    OperationStatus.PROCESSING,
                    increment_attempts=True
                )
                
                # Get hotel integrations
                integrations = await self.integration_service.get_hotel_integrations(queue_item.hotel_id)
                
                if not integrations:
                    logger.warning(f"No integrations found for hotel {queue_item.hotel_id}")
                    await self._handle_queue_item_failure(
                        queue_item, 
                        "No integrations configured for hotel"
                    )
                    return
                
                # Filter integrations if specific providers are requested
                target_providers = queue_item.data.get("target_providers")
                if target_providers:
                    integrations = [
                        integration for integration in integrations
                        if integration.get("provider", {}).get("name") in target_providers
                    ]
                
                # Process sync for each integration
                operation_results = []
                for integration in integrations:
                    if not integration.get("is_enabled"):
                        continue
                    
                    result = await self._process_integration_sync(queue_item, integration)
                    operation_results.append(result)
                
                # Determine overall success
                successful_operations = [r for r in operation_results if r.get("success")]
                
                if successful_operations:
                    await self.queue_service.update_queue_item_status(
                        queue_item.id, 
                        OperationStatus.SUCCESS
                    )
                    logger.info(f"Queue item {queue_item.id} processed successfully")
                else:
                    await self._handle_queue_item_failure(
                        queue_item,
                        "All integration syncs failed"
                    )
                
                # Remove completed item from queue
                await self.queue_service.remove_from_queue(queue_item.id)
                
            except Exception as e:
                logger.error(f"Error processing queue item {queue_item.id}: {e}")
                await self._handle_queue_item_failure(queue_item, str(e))
    
    async def _process_integration_sync(self, queue_item, integration) -> Dict[str, Any]:
        """Process sync for a specific integration."""
        try:
            # Create sync operation record
            operation_data = SyncOperationCreate(
                hotel_integration_id=UUID(integration["id"]),
                operation_type=queue_item.operation_type,
                direction=Direction.OUTBOUND,
                data_payload=queue_item.data,
                scheduled_at=datetime.utcnow()
            )
            
            operation = await self.operation_service.create_operation(operation_data)
            
            # Update operation status to processing
            await self.operation_service.update_operation_status(
                operation.id, 
                OperationStatus.PROCESSING
            )
            
            # Process based on operation type
            success = False
            error_details = None
            
            try:
                if queue_item.operation_type == OperationType.RATE_UPDATE:
                    success = await self._process_rate_update(queue_item, integration)
                elif queue_item.operation_type == OperationType.AVAILABILITY_UPDATE:
                    success = await self._process_availability_update(queue_item, integration)
                elif queue_item.operation_type == OperationType.FULL_SYNC:
                    success = await self._process_full_sync(queue_item, integration)
                else:
                    error_details = {"error": f"Unsupported operation type: {queue_item.operation_type}"}
                
            except Exception as sync_error:
                logger.error(f"Sync error for integration {integration['id']}: {sync_error}")
                error_details = {"error": str(sync_error), "type": "sync_error"}
            
            # Update operation status
            final_status = OperationStatus.SUCCESS if success else OperationStatus.FAILED
            await self.operation_service.update_operation_status(
                operation.id,
                final_status,
                error_details
            )
            
            return {
                "success": success,
                "operation_id": operation.id,
                "integration_id": integration["id"],
                "error_details": error_details
            }
            
        except Exception as e:
            logger.error(f"Error processing integration sync: {e}")
            return {
                "success": False,
                "integration_id": integration["id"],
                "error_details": {"error": str(e), "type": "processing_error"}
            }
    
    async def _process_rate_update(self, queue_item, integration) -> bool:
        """Process rate update for an integration."""
        try:
            provider_name = integration.get("provider", {}).get("name")
            
            # TODO: Implement provider-specific rate update logic
            # For now, simulate processing
            logger.info(f"Processing rate update for {provider_name}")
            
            # Simulate API call delay
            await asyncio.sleep(1)
            
            # Mock success for demonstration
            return True
            
        except Exception as e:
            logger.error(f"Rate update failed: {e}")
            return False
    
    async def _process_availability_update(self, queue_item, integration) -> bool:
        """Process availability update for an integration."""
        try:
            provider_name = integration.get("provider", {}).get("name")
            
            # TODO: Implement provider-specific availability update logic
            logger.info(f"Processing availability update for {provider_name}")
            
            # Simulate API call delay
            await asyncio.sleep(1)
            
            # Mock success for demonstration
            return True
            
        except Exception as e:
            logger.error(f"Availability update failed: {e}")
            return False
    
    async def _process_full_sync(self, queue_item, integration) -> bool:
        """Process full sync for an integration."""
        try:
            provider_name = integration.get("provider", {}).get("name")
            
            # TODO: Implement provider-specific full sync logic
            logger.info(f"Processing full sync for {provider_name}")
            
            # Simulate longer processing time for full sync
            await asyncio.sleep(3)
            
            # Mock success for demonstration
            return True
            
        except Exception as e:
            logger.error(f"Full sync failed: {e}")
            return False
    
    async def _handle_queue_item_failure(self, queue_item, error_message: str):
        """Handle queue item failure with retry logic."""
        try:
            if queue_item.attempts >= queue_item.max_attempts:
                # Max attempts reached, mark as failed
                await self.queue_service.update_queue_item_status(
                    queue_item.id, 
                    OperationStatus.FAILED
                )
                logger.error(f"Queue item {queue_item.id} failed after {queue_item.attempts} attempts: {error_message}")
                
                # Remove failed item from queue
                await self.queue_service.remove_from_queue(queue_item.id)
            else:
                # Schedule retry with exponential backoff
                retry_delay = min(300, 30 * (2 ** queue_item.attempts))  # Max 5 minutes
                retry_time = datetime.utcnow() + timedelta(seconds=retry_delay)
                
                # Update queue item for retry
                await self.queue_service.update_queue_item_status(
                    queue_item.id, 
                    OperationStatus.RETRY
                )
                
                logger.info(f"Queue item {queue_item.id} scheduled for retry in {retry_delay} seconds")
                
        except Exception as e:
            logger.error(f"Error handling queue item failure: {e}")


# Global sync processor instance
sync_processor = SyncProcessorService()
