import logging
from datetime import datetime, timedelta, timezone
from typing import Optional

from app.core.config import settings
from app.schemas.agent import SessionCreate, SessionInDB
from .session_db_service import SessionDBService
from .message_db_service import MessageDBService

logger = logging.getLogger(__name__)


class SessionManagementService:
    """
    Service for managing user sessions.
    """

    async def get_or_create_session(self, session_id: Optional[str] = None, user_id: Optional[str] = None, access_token: Optional[str] = None) -> SessionInDB:
        """
        Get an existing session or create a new one if it doesn't exist.
        """
        db_service = SessionDBService(access_token=access_token)
        if session_id:
            session = await db_service.get_session(session_id)
            if session:
                return SessionInDB(**session.dict())

        if not user_id:
            user_id = "anonymous"

        expires_at = datetime.now(timezone.utc) + \
            timedelta(hours=settings.SESSION_EXPIRE_HOURS)
        session_data = await db_service.create_session(
            SessionCreate(
                user_id=user_id,
                expires_at=expires_at
            )
        )
        return SessionInDB(**session_data.dict())

    async def create_session(
        self,
        user_id: str,
        expires_at: Optional[datetime] = None,
        access_token: Optional[str] = None
    ) -> SessionInDB:
        """
        Create a new user session.
        """
        if not expires_at:
            expires_at = datetime.now(
                timezone.utc) + timedelta(hours=settings.SESSION_EXPIRE_HOURS)

        db_service = SessionDBService(access_token=access_token)
        session_data = await db_service.create_session(
            SessionCreate(
                user_id=user_id,
                expires_at=expires_at
            )
        )
        return SessionInDB(**session_data.dict())

    async def get_session(self, session_id: str, access_token: Optional[str] = None) -> Optional[SessionInDB]:
        """
        Get a session by ID.
        """
        try:
            db_service = SessionDBService(access_token=access_token)
            session_data = await db_service.get_session(session_id)
            if not session_data:
                return None
            return SessionInDB(**session_data.dict())
        except Exception as e:
            logger.warning(
                f"Could not retrieve session {session_id}: {str(e)}")
            return None

    async def delete_session(self, session_id: str, access_token: Optional[str] = None) -> bool:
        """
        Delete a session and all associated messages.
        """
        session_db_service = SessionDBService(access_token=access_token)
        message_db_service = MessageDBService(access_token=access_token)
        await message_db_service.delete_messages_by_session(session_id)
        return await session_db_service.delete_session(session_id)


session_management_service = SessionManagementService()
