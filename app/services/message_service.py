import logging
from typing import Dict, Any, Optional, List, Tuple

from fastapi import HTT<PERSON>Ex<PERSON>, status

from app.schemas.agent import MessageCreate, MessageInDB
from .message_db_service import MessageDBService

logger = logging.getLogger(__name__)


class MessageService:
    """
    Service for handling message-related operations.
    """

    async def add_message(
        self,
        user_id: str,
        agent_id: str,
        content: str,
        role: str,
        metadata: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        access_token: Optional[str] = None
    ) -> MessageInDB:
        """
        Add a message to the conversation.
        """
        try:
            db_service = MessageDBService(access_token=access_token)
            message_data = await db_service.create_message(
                user_id=user_id,
                agent_id=agent_id,
                content=content,
                role=role,
                metadata=metadata or {},
                session_id=session_id
            )
            return MessageInDB(**message_data.dict())
        except Exception as e:
            logger.error(f"Error adding message: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add message to conversation"
            )

    async def get_messages(
        self,
        agent_id: str,
        limit: int = 10,
        cursor: Optional[str] = None,
        session_id: Optional[str] = None,
        access_token: Optional[str] = None
    ) -> Tuple[List[MessageInDB], Optional[str]]:
        """
        Get messages for an agent with pagination.
        """
        try:
            db_service = MessageDBService(access_token=access_token)
            messages, next_cursor = await db_service.get_messages(
                agent_id=agent_id,
                session_id=session_id,
                limit=limit,
                cursor=cursor
            )
            return messages, next_cursor
        except Exception as e:
            logger.error(f"Error retrieving messages: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve messages"
            )


message_service = MessageService()
