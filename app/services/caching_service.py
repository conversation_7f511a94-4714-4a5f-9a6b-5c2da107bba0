import asyncio
import logging
import time
from typing import Dict, List, Optional

from .message_db_service import MessageDBService

logger = logging.getLogger(__name__)


class CachingService:
    """
    Service for managing caching of conversation history.
    """

    def __init__(self):
        self._conversation_cache: Dict[str, List[dict]] = {}
        self._conversation_cache_lock = asyncio.Lock()
        self._conversation_cache_ttl: Dict[str, float] = {}  # TTL timestamps

    async def get_cached_conversation_history(self, session_id: str, agent_id: str, access_token: Optional[str]) -> List[dict]:
        """Get conversation history from cache or database with intelligent caching."""
        cache_key = f"{session_id}:{agent_id}"
        current_time = time.time()
        cache_ttl_seconds = 300  # 5 minutes cache TTL

        async with self._conversation_cache_lock:
            if (cache_key in self._conversation_cache and
                cache_key in self._conversation_cache_ttl and
                    current_time - self._conversation_cache_ttl[cache_key] < cache_ttl_seconds):
                logger.info(
                    f"Using cached conversation history for session {session_id}")
                return self._conversation_cache[cache_key]

        logger.info(
            f"Fetching conversation history from database for session {session_id}")
        try:
            db_service = MessageDBService(access_token=access_token)
            messages, _ = await asyncio.wait_for(
                db_service.get_messages(
                    agent_id=agent_id,
                    session_id=session_id,
                    limit=20
                ),
                timeout=2.0
            )
            conversation_history = [
                {"role": msg.role, "content": msg.content} for msg in messages]

            async with self._conversation_cache_lock:
                self._conversation_cache[cache_key] = conversation_history
                self._conversation_cache_ttl[cache_key] = current_time

            logger.info(
                f"Cached {len(conversation_history)} messages for session {session_id}")
            return conversation_history

        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout fetching history for session {session_id}, using empty history")
            async with self._conversation_cache_lock:
                self._conversation_cache[cache_key] = []
                self._conversation_cache_ttl[cache_key] = current_time
            return []
        except Exception as e:
            logger.warning(
                f"Error fetching history for session {session_id}: {e}, using empty history")
            return []

    async def invalidate_conversation_cache(self, session_id: str, agent_id: str):
        """Invalidate conversation cache when new messages are added."""
        cache_key = f"{session_id}:{agent_id}"
        async with self._conversation_cache_lock:
            if cache_key in self._conversation_cache:
                del self._conversation_cache[cache_key]
            if cache_key in self._conversation_cache_ttl:
                del self._conversation_cache_ttl[cache_key]
        logger.debug(
            f"Invalidated conversation cache for session {session_id}")


caching_service = CachingService()
