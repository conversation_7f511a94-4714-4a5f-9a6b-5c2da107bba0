import asyncio
import logging
import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ct, <PERSON><PERSON>, <PERSON><PERSON>

from agents import Agent, <PERSON><PERSON><PERSON><PERSON><PERSON>, Runner
from agents.tracing import set_tracing_disabled
from openai.types.responses import ResponseTextDeltaEvent

from app.agents.definitions import hotel_manager_agent
from app.agents.specialized_agents import get_specialized_agent, specialized_agent_system
from app.core.config import settings
from app.core.localization import localization_service
from app.core.openai_config import azure_openai_client
from app.core.sessions import CachedReadOnlySupabaseSession, SupabaseSession
from app.schemas.agent import SessionInDB
from .agent_management_service import agent_management_service
from .caching_service import caching_service
from .session_db_service import SessionDBService
from .session_management_service import session_management_service
from .supabase_tracing_service import supabase_tracing_service as tracing_service

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ChatService:
    """
    Service for handling chat interactions with AI agents.
    """

    def __init__(self):
        self._initialized = False
        self._max_retries = settings.DEFAULT_MAX_RETRIES
        self._retry_delay = settings.DEFAULT_RETRY_DELAY

    async def initialize(self):
        """Initialize the chat service asynchronously."""
        if not self._initialized:
            await azure_openai_client.initialize()
            set_tracing_disabled(True)
            self._initialized = True

    def get_agent(self, agent_type: str = "hotel_manager", message: str = None) -> Agent:
        """Get a specialized agent based on the user's message."""
        try:
            if message:
                logger.info(
                    f"Getting specialized agent for message: {message[:50]}...")
                return get_specialized_agent(message)
            else:
                logger.info("No message provided, using triage agent")
                return specialized_agent_system.get_agent_for_intent("general")
        except Exception as e:
            logger.error(f"Error creating specialized agent: {e}")
            return hotel_manager_agent

    def _select_agent(self, input_data: Dict[str, Any]) -> Tuple[Agent, Dict[str, Any]]:
        """Select the appropriate agent based on input data."""
        message = input_data.get('message', '').lower()
        try:
            lang = localization_service.detect_language(message)
            instructions = localization_service.get_translation(
                lang, "hotel_manager_agent")
            if not instructions:
                lang = "en"
                instructions = localization_service.get_translation(
                    lang, "hotel_manager_agent")
        except ValueError as e:
            logger.warning(
                f"Language detection failed: {e}. Falling back to English.")
            lang = "en"
            instructions = localization_service.get_translation(
                lang, "hotel_manager_agent")

        if not instructions:
            raise ValueError("Default language instructions not found.")

        user_message = input_data.get('message', '')
        agent = self.get_agent("hotel_manager", user_message)
        agent.instructions = instructions['instructions']
        detected_intent = specialized_agent_system.analyze_user_message(
            user_message)
        context_update = {
            'intent': detected_intent,
            'language': lang,
            'agent_type': agent.name
        }
        if any(keyword in message for keyword in ["technical", "bug", "error", "issue"]):
            context_update['needs_technical_support'] = True
        return agent, context_update

    async def _check_safety(self, input_data: Dict[str, Any]) -> Any:
        """Perform safety checks on the input data."""
        from app.agents.safety import check_input_safety
        return await check_input_safety(input_data)

    async def chat(
        self,
        message: str,
        session_id: Optional[str] = None,
        agent_type: str = "hotel_manager",
        user_id: Optional[str] = None,
        background_tasks: Optional[Any] = None,
        access_token: Optional[str] = None,
        status_callback: Optional[callable] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Process a chat message with an agent."""
        try:
            if not session_id:
                session_id = str(uuid.uuid4())
            if not user_id:
                user_id = "anonymous"

            input_data = {'user_id': user_id, 'message': message,
                          'session_id': session_id, **kwargs}
            if status_callback:
                await status_callback("Performing safety checks...")
            safety_result = await self._check_safety(input_data)
            if not safety_result.passed:
                if status_callback:
                    await status_callback(f"Safety check failed: {safety_result.reason}")
                return {'success': False, 'error': f"Safety check failed: {safety_result.reason}", 'session_id': session_id, 'timestamp': datetime.now(timezone.utc).isoformat(), 'metadata': safety_result.metadata}
            if status_callback:
                await status_callback("Safety checks passed")

            if status_callback:
                await status_callback("Analyzing your request and selecting the best agent...")
            try:
                agent, context_update = self._select_agent(
                    safety_result.sanitized_input or input_data)
                if status_callback:
                    agent_status_map = {
                        "hotel_triage": "Routing your request to the appropriate specialist...",
                        "hotel_listing_specialist": "Fetching available hotels...",
                        "price_update_specialist": "Preparing price update form...",
                        "room_info_specialist": "Gathering room information...",
                        "form_submission_processor": "Processing your form submission...",
                        "clarification_specialist": "Understanding your request...",
                        "HM Hotel Manager": "Processing your hotel management request..."
                    }
                    status_msg = agent_status_map.get(
                        agent.name, f"Selected {agent.name} to handle your request...")
                    await status_callback(status_msg)
            except Exception as e:
                if status_callback:
                    await status_callback(f"Error selecting agent: {str(e)}")
                return {'success': False, 'error': f"Failed to select agent: {str(e)}", 'session_id': session_id, 'timestamp': datetime.now(timezone.utc).isoformat()}

            context = {'user_id': user_id, 'session_id': session_id, 'message': message,
                       'access_token': access_token, **kwargs, **context_update, 'safety_metadata': safety_result.metadata}
            if status_callback:
                await status_callback("Processing your request with the selected agent...")
            response_text = await self._get_agent_response(agent, message, context)
            if status_callback:
                await status_callback("Response generated successfully!")

            if background_tasks:
                background_tasks.add_task(
                    self._save_messages_and_traces_with_session,
                    user_id=user_id,
                    session_id=session_id,
                    message=message,
                    response=response_text,
                    agent_name=agent.name,
                    access_token=access_token,
                    safety_metadata=safety_result.metadata
                )

            return {'success': True, 'response': response_text, 'session_id': session_id, 'timestamp': datetime.now(timezone.utc).isoformat(), 'agent_type': agent.name}
        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            logger.error(error_msg, exc_info=True)
            if tracing_service:
                tracing_service.record_error({'error_type': type(e).__name__, 'error_message': str(e), 'traceback': str(
                    e.__traceback__) if hasattr(e, '__traceback__') else None, 'context': {'input': message, 'user_id': user_id, **kwargs}, 'session_id': session_id, 'user_id': user_id})
            return {'success': False, 'error': "An error occurred while processing your request", 'session_id': session_id, 'timestamp': datetime.now(timezone.utc).isoformat(), 'internal_error': str(e) if settings.DEBUG else None}

    async def chat_streamed(
        self,
        message: str,
        session_id: Optional[str] = None,
        agent_type: str = "hotel_manager",
        user_id: Optional[str] = None,
        background_tasks: Optional[Any] = None,
        access_token: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Process a chat message with an agent using streaming."""
        try:
            if not session_id:
                session_id = str(uuid.uuid4())
            if not user_id:
                user_id = "anonymous"

            input_data = {'user_id': user_id, 'message': message,
                          'session_id': session_id, **kwargs}
            yield {'type': 'status', 'message': 'Performing safety checks...'}
            safety_result = await self._check_safety(input_data)
            if not safety_result.passed:
                yield {'type': 'error', 'message': f"Safety check failed: {safety_result.reason}"}
                return
            yield {'type': 'status', 'message': 'Safety checks passed'}

            yield {'type': 'status', 'message': 'Analyzing your request and selecting the best agent...'}
            try:
                agent, context_update = self._select_agent(
                    safety_result.sanitized_input or input_data)
                agent_status_map = {
                    "hotel_triage": "Routing your request to the appropriate specialist...",
                    "hotel_listing_specialist": "Fetching available hotels...",
                    "price_update_specialist": "Preparing price update form...",
                    "room_info_specialist": "Gathering room information...",
                    "form_submission_processor": "Processing your form submission...",
                    "clarification_specialist": "Understanding your request...",
                    "HM Hotel Manager": "Processing your hotel management request..."
                }
                status_msg = agent_status_map.get(
                    agent.name, f"Selected {agent.name} to handle your request...")
                yield {'type': 'status', 'message': status_msg}
            except Exception as e:
                yield {'type': 'error', 'message': f"Error selecting agent: {str(e)}"}
                return

            context = {'user_id': user_id, 'session_id': session_id, 'message': message,
                       'access_token': access_token, **kwargs, **context_update, 'safety_metadata': safety_result.metadata}
            yield {'type': 'status', 'message': 'Processing your request with the selected agent...'}

            session_data = await self._get_session_for_history(session_id, user_id, access_token)
            agent_id = await agent_management_service.get_agent_id(agent.name, access_token)
            cached_history = await caching_service.get_cached_conversation_history(session_id, agent_id, access_token)
            cached_session = CachedReadOnlySupabaseSession(
                session_data=session_data, agent_id=agent_id, access_token=access_token, cached_history=cached_history)

            result = Runner.run_streamed(
                agent, message, session=cached_session)
            response_text = ""
            try:
                async for event in result.stream_events():
                    try:
                        if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                            delta_text = event.data.delta
                            response_text += delta_text
                            yield {'type': 'text_delta', 'delta': delta_text}
                        elif event.type == "run_item_stream_event":
                            if event.item.type == "tool_call_item":
                                tool_name = "unknown tool"
                                for attr in ['name', 'function_name', 'tool_name', 'function', 'tool']:
                                    if hasattr(event.item, attr):
                                        tool_value = getattr(event.item, attr)
                                        if isinstance(tool_value, str):
                                            tool_name = tool_value
                                            break
                                        elif hasattr(tool_value, 'name'):
                                            tool_name = tool_value.name
                                            break
                                yield {'type': 'status', 'message': f'Calling tool: {tool_name}...'}
                            elif event.item.type == "tool_call_output_item":
                                yield {'type': 'status', 'message': 'Tool execution completed'}
                            elif event.item.type == "message_output_item":
                                response_text = ItemHelpers.text_message_output(
                                    event.item)
                        elif event.type == "agent_updated_stream_event":
                            yield {'type': 'status', 'message': f'Transferring to {event.new_agent.name}...'}
                    except Exception as e:
                        logger.warning(
                            f"Error processing individual stream event: {e}")
                        continue
            except Exception as e:
                logger.error(f"Error in stream processing: {e}")
                yield {'type': 'status', 'message': 'Processing stream events...'}

            yield {'type': 'status', 'message': 'Response generated successfully!'}
            if background_tasks:
                background_tasks.add_task(
                    self._save_messages_and_traces_with_session,
                    user_id=user_id,
                    session_id=session_id,
                    message=message,
                    response=response_text,
                    agent_name=agent.name,
                    access_token=access_token,
                    safety_metadata=safety_result.metadata
                )

            yield {'type': 'response', 'data': {'success': True, 'response': response_text, 'session_id': session_id, 'timestamp': datetime.now(timezone.utc).isoformat(), 'agent_type': agent.name}}
        except Exception as e:
            error_msg = f"Error processing streaming message: {str(e)}"
            logger.error(error_msg, exc_info=True)
            yield {'type': 'error', 'message': error_msg}

    async def _get_agent_response(self, agent: Agent, message: str, context: Dict[str, Any]) -> str:
        """Get the agent's response without blocking on session operations."""
        session_id = context.get('session_id')
        access_token = context.get('access_token')
        agent_id = await agent_management_service.get_agent_id(agent.name, access_token)
        cached_history = await caching_service.get_cached_conversation_history(session_id, agent_id, access_token)

        try:
            session_data = await self._get_session_for_history(session_id, context.get('user_id'), access_token)
        except Exception as e:
            logger.warning(
                f"Could not load session data for {session_id}: {e}, using minimal session")
            from app.schemas.agent import SessionInDB
            session_data = SessionInDB(
                id=session_id,
                user_id=context.get('user_id', 'anonymous'),
                expires_at=datetime.now(timezone.utc) + timedelta(hours=24),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

        cached_session = CachedReadOnlySupabaseSession(
            session_data=session_data, agent_id=agent_id, access_token=access_token, cached_history=cached_history)
        response = await Runner.run(agent, message, session=cached_session)
        return response.final_output if hasattr(response, 'final_output') else str(response)

    async def _get_session_for_history(self, session_id: str, user_id: Optional[str], access_token: Optional[str]) -> SessionInDB:
        """Get session for conversation history, with fast timeout."""
        db_service = SessionDBService(access_token=access_token)
        session = await db_service.get_session(session_id)
        if session:
            return SessionInDB(**session.dict())
        return SessionInDB(
            id=session_id,
            user_id=user_id or 'anonymous',
            expires_at=datetime.now(timezone.utc) + timedelta(hours=24),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

    async def _save_messages_and_traces_with_session(
        self,
        user_id: str,
        session_id: str,
        message: str,
        response: str,
        agent_name: str,
        access_token: Optional[str],
        safety_metadata: Dict[str, Any]
    ):
        """Save messages and traces in background, ensuring session exists."""
        try:
            agent_id = await agent_management_service.get_agent_id(agent_name, access_token)
            session_data = await session_management_service.get_or_create_session(session_id, user_id, access_token=access_token)
            session = SupabaseSession(session_data, agent_id, access_token)
            await session.add_items([
                {"role": "user", "content": message},
                {"role": "assistant", "content": response}
            ])
            await caching_service.invalidate_conversation_cache(session_id, agent_id)
            if tracing_service:
                trace_data = {'user_id': user_id, 'input': message[:500], 'output': response[:1000] if isinstance(
                    response, str) else str(response)[:1000], 'agent_id': agent_name, 'safety_checks': safety_metadata, 'success': True}
                asyncio.create_task(tracing_service.store_trace_async(
                    trace_data=trace_data, session_id=session_id, user_id=user_id))
        except Exception as e:
            logger.error(
                f"Error saving messages and traces: {str(e)}", exc_info=True)


chat_service = ChatService()
