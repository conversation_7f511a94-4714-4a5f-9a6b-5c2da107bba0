import os
import json
import boto3
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from contextlib import contextmanager
from uuid import uuid4
from openai.types.beta.threads import Run

from app.core.config import settings

class TracingService:
    """Service for handling tracing of agent executions and storing in DynamoDB"""
    
    def __init__(self):
        self._dynamodb = None
        self._table_name = settings.DYNAMODB_TABLE_NAME
        self._ensure_table_exists()
    
    @property
    def dynamodb(self):
        """Lazy initialization of DynamoDB client"""
        if self._dynamodb is None:
            self._dynamodb = boto3.resource(
                'dynamodb',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION
            )
        return self._dynamodb
    
    @property
    def table(self):
        """Get the DynamoDB table"""
        return self.dynamodb.Table(self._table_name)
    
    def _ensure_table_exists(self):
        """Ensure the DynamoDB table exists, create if it doesn't"""
        try:
            existing_tables = self.dynamodb.meta.client.list_tables()['TableNames']
            if self._table_name not in existing_tables:
                self._create_table()
        except Exception as e:
            print(f"Error checking/creating DynamoDB table: {e}")
    
    def _create_table(self):
        """Create the DynamoDB table"""
        try:
            table = self.dynamodb.create_table(
                TableName=self._table_name,
                KeySchema=[
                    {'AttributeName': 'id', 'KeyType': 'HASH'},  # Partition key
                    {'AttributeName': 'sessionId', 'KeyType': 'RANGE'}  # Sort key
                ],
                AttributeDefinitions=[
                    {'AttributeName': 'id', 'AttributeType': 'S'},
                    {'AttributeName': 'sessionId', 'AttributeType': 'S'},
                    {'AttributeName': 'createdAt', 'AttributeType': 'S'}
                ],
                BillingMode='PAY_PER_REQUEST',
                GlobalSecondaryIndexes=[
                    {
                        'IndexName': 'SessionIndex',
                        'KeySchema': [
                            {'AttributeName': 'sessionId', 'KeyType': 'HASH'},
                            {'AttributeName': 'createdAt', 'KeyType': 'RANGE'}
                        ],
                        'Projection': {
                            'ProjectionType': 'ALL'
                        }
                    }
                ]
            )
            table.wait_until_exists()
            print(f"Created DynamoDB table: {self._table_name}")
        except Exception as e:
            print(f"Error creating DynamoDB table: {e}")
    
    def _convert_floats_to_decimals(self, obj):
        """Recursively convert float values to Decimal for DynamoDB compatibility"""
        from decimal import Decimal
        import json
        
        if isinstance(obj, float):
            return Decimal(str(obj))
        elif isinstance(obj, dict):
            return {k: self._convert_floats_to_decimals(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._convert_floats_to_decimals(x) for x in obj]
        return obj
    
    def store_trace(self, trace_data: Dict[str, Any], session_id: str) -> str:
        """Store a trace in DynamoDB with enhanced structure for tool calls and AI responses"""
        trace_id = str(uuid4())
        timestamp = datetime.utcnow().isoformat()
        
        try:
            # Extract message content if it exists
            message = trace_data.pop('message', None)
            
            # Prepare the base item
            item = {
                'id': trace_id,
                'sessionId': session_id,
                'createdAt': timestamp,
                'traceData': self._convert_floats_to_decimals(trace_data.copy()),
                'metadata': {
                    'agentType': str(trace_data.get('agent_type', 'unknown')),  # Ensure string type
                    'timestamp': timestamp,
                    'status': str(trace_data.get('status', 'completed'))  # Ensure string type
                }
            }
            
            # Add message data if available
            if message:
                item['userMessage'] = str(message)
            
            # Add tool calls if present
            if 'tool_calls' in trace_data and trace_data['tool_calls'] is not None:
                item['toolCalls'] = self._convert_floats_to_decimals(trace_data['tool_calls'])
                
            # Add AI response if present
            if 'ai_response' in trace_data and trace_data['ai_response'] is not None:
                item['aiResponse'] = self._convert_floats_to_decimals(trace_data['ai_response'])
            
            # Ensure all numeric values are properly converted to Decimal
            item = self._convert_floats_to_decimals(item)
            
            # Store in DynamoDB
            self.table.put_item(Item=item)
            return trace_id
            
        except Exception as e:
            print(f"Error storing trace in DynamoDB: {e}")
            import traceback
            traceback.print_exc()
            return ""

    async def store_trace_async(self, trace_data: Dict[str, Any], session_id: str) -> str:
        """Asynchronously store a trace in DynamoDB."""
        return await asyncio.to_thread(self.store_trace, trace_data, session_id)
    
    def get_all_traces(self, limit: int = 100) -> list:
        """Retrieve all traces, most recent first"""
        try:
            print(f"Scanning DynamoDB table {self._table_name} for all traces")
            
            # First try to use the createdAt index if it exists
            try:
                response = self.table.scan(
                    Limit=limit,
                    Select='ALL_ATTRIBUTES'
                )
                items = response.get('Items', [])
                
                # Sort by createdAt in descending order (newest first)
                items.sort(key=lambda x: x.get('createdAt', ''), reverse=True)
                
                print(f"Found {len(items)} total traces")
                return items
                
            except Exception as e:
                print(f"Error scanning table: {e}")
                import traceback
                traceback.print_exc()
                return []
                
        except Exception as e:
            print(f"Error retrieving all traces from DynamoDB: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def get_traces_for_session(self, session_id: str, limit: int = 100) -> list:
        """Retrieve traces for a specific session"""
        try:
            print(f"Querying DynamoDB table {self._table_name} for session_id: {session_id}")
            
            # First, try to query using the GSI
            try:
                response = self.table.query(
                    IndexName='SessionIndex',
                    KeyConditionExpression='sessionId = :sid',
                    ExpressionAttributeValues={
                        ':sid': session_id
                    },
                    Limit=limit,
                    ScanIndexForward=False  # Get most recent first
                )
                items = response.get('Items', [])
                print(f"Found {len(items)} traces for session {session_id} using GSI")
                return items
                
            except self.dynamodb.meta.client.exceptions.ResourceNotFoundException:
                print("GSI not found, trying table scan as fallback...")
                # Fallback to scan if GSI doesn't exist
                response = self.table.scan(
                    FilterExpression='sessionId = :sid',
                    ExpressionAttributeValues={
                        ':sid': session_id
                    },
                    Limit=limit
                )
                items = response.get('Items', [])
                # Sort by createdAt in descending order (newest first)
                items.sort(key=lambda x: x.get('createdAt', ''), reverse=True)
                print(f"Found {len(items)} traces for session {session_id} using scan")
                return items
                
        except Exception as e:
            print(f"Error retrieving traces from DynamoDB: {e}")
            import traceback
            traceback.print_exc()
            return []
            
    def record_error(self, error_data: Dict[str, Any]) -> str:
        """
        Record an error in the tracing system
        
        Args:
            error_data: Dictionary containing error information including:
                - error_type: Type/class of the error
                - error_message: Error message
                - traceback: Full traceback if available
                - context: Additional context about where the error occurred
                - session_id: Optional session ID
                - user_id: Optional user ID
                
        Returns:
            str: Error ID or empty string if recording failed
        """
        try:
            error_id = str(uuid4())
            timestamp = datetime.utcnow().isoformat()
            
            item = {
                'id': f"error_{error_id}",
                'sessionId': error_data.get('session_id', 'unknown'),
                'type': 'error',
                'createdAt': timestamp,
                'errorData': error_data,
                'metadata': {
                    'timestamp': timestamp,
                    'user_id': error_data.get('user_id')
                }
            }
            
            # Store in DynamoDB
            self.table.put_item(Item=item)
            return error_id
            
        except Exception as e:
            print(f"Error recording error in tracing system: {e}")
            import traceback
            traceback.print_exc()
            return ""

# Global instance
tracing_service = TracingService()

# Context manager for tracing
@contextmanager
def trace_agent_run(agent_name: str, session_id: str, **kwargs):
    """Context manager for tracing agent runs"""
    trace_id = None
    start_time = datetime.utcnow()
    
    try:
        # Create initial trace
        trace_data = {
            'agent_type': agent_name,
            'status': 'started',
            'start_time': start_time.isoformat(),
            'metadata': kwargs
        }
        trace_id = tracing_service.store_trace(trace_data, session_id)
        
        # Yield both the trace_id and a function to update the trace with the response
        response_data = {}
        
        def update_response(**kwargs):
            nonlocal response_data
            response_data.update(kwargs)
            
        yield trace_id, update_response
        
        # Update trace on success
        end_time = datetime.utcnow()
        trace_data.update({
            'status': 'completed',
            'end_time': end_time.isoformat(),
            'duration_seconds': (end_time - start_time).total_seconds(),
            **response_data  # Include any response data that was added
        })
        tracing_service.store_trace(trace_data, session_id)
        
    except Exception as e:
        # Update trace on error
        if trace_id:
            end_time = datetime.utcnow()
            trace_data.update({
                'status': 'error',
                'error': str(e),
                'end_time': end_time.isoformat(),
                'duration_seconds': (end_time - start_time).total_seconds()
            })
            tracing_service.store_trace(trace_data, session_id)
        raise

def trace_run(run: Run, session_id: str):
    """Trace an OpenAI agent run with detailed tool calls and responses"""
    try:
        # Extract tool calls and responses
        tool_calls = []
        if hasattr(run, 'required_action') and run.required_action:
            for tool_call in run.required_action.submit_tool_outputs.tool_calls:
                tool_calls.append({
                    'id': tool_call.id,
                    'type': tool_call.function.name,
                    'arguments': json.loads(tool_call.function.arguments)
                })
        
        # Extract AI response
        ai_response = None
        if hasattr(run, 'last_message') and run.last_message:
            ai_response = {
                'content': run.last_message.content[0].text.value if hasattr(run.last_message, 'content') else str(run.last_message),
                'role': run.last_message.role
            }
        
        trace_data = {
            'agent_type': 'openai_agent',
            'run_id': run.id,
            'status': run.status,
            'created_at': datetime.utcnow().isoformat(),
            'metadata': {
                'model': run.model,
                'instructions': run.instructions,
                'tools': [str(tool) for tool in run.tools] if hasattr(run, 'tools') else []
            },
            'tool_calls': tool_calls if tool_calls else None,
            'ai_response': ai_response
        }
        return tracing_service.store_trace(trace_data, session_id)
    except Exception as e:
        print(f"Error tracing run: {e}")
        import traceback
        traceback.print_exc()
        return ""
