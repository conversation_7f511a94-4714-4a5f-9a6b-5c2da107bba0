import logging
from typing import List, Optional

from fastapi import HTTPException, status
from supabase import Client

from app.core.supabase_client import supabase, supabase_service
from app.schemas.agent import Agent, AgentCreate

logger = logging.getLogger(__name__)


class AgentDBService:
    """Service for handling agent-related database operations."""

    AGENTS_TABLE: str = 'agents'

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)

    async def create_agent(self, user_id: str, agent_data: AgentCreate) -> Agent:
        """Create a new agent."""
        try:
            agent_dict = agent_data.dict()
            agent_dict['user_id'] = user_id
            result = self.supabase.table(
                self.AGENTS_TABLE).insert(agent_dict).execute()
            if not result.data:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create agent"
                )
            return Agent(**result.data[0])
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating agent: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating the agent"
            )

    async def get_agent_by_name(self, agent_name: str) -> Optional[Agent]:
        """Get an agent by name."""
        try:
            result = self.supabase.table(self.AGENTS_TABLE).select(
                '*').eq('name', agent_name).execute()
            if not result.data:
                return None
            return Agent(**result.data[0])
        except Exception as e:
            logger.error(
                f"Error getting agent by name {agent_name}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving the agent"
            )

    async def get_agent(self, agent_id: str) -> Optional[Agent]:
        """Get an agent by ID."""
        try:
            result = self.supabase.table(self.AGENTS_TABLE).select(
                '*').eq('id', agent_id).execute()
            if not result.data:
                return None
            return Agent(**result.data[0])
        except Exception as e:
            logger.error(
                f"Error getting agent {agent_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving the agent"
            )

    async def get_user_agents(self, user_id: str) -> List[Agent]:
        """Get all agents for a user."""
        try:
            result = self.supabase.table(self.AGENTS_TABLE).select('*').eq(
                'user_id', user_id).order('created_at', desc=True).execute()
            return [Agent(**agent) for agent in (result.data or [])]
        except Exception as e:
            logger.error(
                f"Error getting agents for user {user_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving agents"
            )
