from agents import Agent, Runner
from openai import APIError
from typing import Dict, Any, Optional, Tuple, Callable, Awaitable, List
import uuid
import logging
import asyncio
from datetime import datetime, timezone

from app.core.config import settings
from app.core.openai_config import azure_openai_client
from app.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>ler
from app.agents.definitions import hotel_manager_agent, FormResponseAgent
from app.agents.safety import check_input_safety, SafetyCheckResult
from .tracing_service import tracing_service, trace_agent_run
from app.core.localization import localization_service
from .database_service import database_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentService:
    """
    Enhanced Agent Service with safety checks and orchestration
    
    This service manages AI agents, handles input validation, and orchestrates
    the flow between different components of the agent system.
    """
    
    def __init__(self):
        self.agents: Dict[str, Agent] = {}
        self._initialized = False
        self._safety_checks_enabled = True
        self._max_retries = 3
        self._retry_delay = 0.5  # seconds
        
    async def initialize(self):
        """Initialize the agent service asynchronously"""
        if not self._initialized:
            # Initialize Azure OpenAI client
            await azure_openai_client.initialize()
            # Initialize agents
            self._initialize_agents()
            self._initialized = True
    
    def _initialize_agents(self):
        """Initialize different types of agents"""
        self.agents["hotel_manager"] = hotel_manager_agent
        self.agents["form_response"] = FormResponseAgent()
        
    # Agent management methods
    async def create_agent(self, user_id: str, name: str, description: str = None, config: dict = None) -> Dict[str, Any]:
        """
        Create a new agent
        
        Args:
            user_id: ID of the user creating the agent
            name: Name of the agent
            description: Optional description
            config: Optional configuration dictionary
            
        Returns:
            Dict containing the created agent data
        """
        return await database_service.create_agent(
            user_id=user_id,
            name=name,
            description=description,
            config=config or {}
        )
        
    async def get_agent(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an agent by ID
        
        Args:
            agent_id: ID of the agent to retrieve
            
        Returns:
            Agent data if found, None otherwise
        """
        return await database_service.get_agent(agent_id)
    
    async def get_user_agents(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all agents for a user
        
        Args:
            user_id: ID of the user
            
        Returns:
            List of agents
        """
        return await database_service.get_user_agents(user_id)
    
    def get_agent(self, agent_type: str = "hotel_manager") -> Agent:
        """Get an agent by type"""
        return self.agents.get(agent_type, self.agents["hotel_manager"])
    
    def get_session(self, session_id: str) -> SQLiteSession:
        """Get or create a session"""
        if session_id not in self.sessions:
            self.sessions[session_id] = SQLiteSession(session_id, settings.CONVERSATION_DB_PATH)
        return self.sessions[session_id]
        
    def _select_agent(self, input_data: Dict[str, Any]) -> Tuple[Agent, Dict[str, Any]]:
        """Select the appropriate agent based on input data.
        
        Args:
            input_data: Dictionary containing input data for agent selection
            
        Returns:
            Tuple of (selected_agent, context_update)
            
        Raises:
            ValueError: If no suitable agent can be found
        """
        # Get the message from input data
        message = input_data.get('message', '').lower()
        
        try:
            # Detect language and get instructions
            lang = localization_service.detect_language(message)
            instructions = localization_service.get_translation(lang, "hotel_manager_agent")
            
            if not instructions:
                raise ValueError(f"No instructions found for language: {lang}")
                
            # Get agent and set instructions
            agent = self.get_agent("hotel_manager")
            agent.instructions = instructions['instructions']
            
        except ValueError as e:
            logger.error(f"Language selection failed: {e}")
            raise ValueError(f"Language not supported or detection failed: {e}")

        context_update = {}
        
        # Add any additional context based on the message
        if any(keyword in message for keyword in ["technical", "bug", "error", "issue"]):
            context_update['needs_technical_support'] = True
        
        return agent, context_update
    
    async def _check_safety(self, input_data: Dict[str, Any]) -> Any:
        """
        Perform safety checks on the input data
        
        Args:
            input_data: Dictionary containing input data to check
            
        Returns:
            SafetyCheckResult with the result of the safety check
        """
        from app.agents.safety import check_input_safety
        return check_input_safety(input_data)
        
    async def chat(
        self,
        message: str,
        session_id: Optional[str] = None,
        agent_type: str = "hotel_manager",
        user_id: Optional[str] = None,
        background_tasks: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Process a chat message with an agent.
        
        Args:
            message: The message to process
            session_id: Optional session ID for conversation continuity
            agent_type: Type of agent to use (default: "hotel_manager")
            user_id: Optional user ID for the request
            **kwargs: Additional arguments to pass to the agent
            
        Returns:
            Dict containing the agent's response and metadata
        """
        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
            
        # Set default user ID if not provided
        if not user_id:
            user_id = "anonymous"
            
        try:
            # 1. Prepare input data
            input_data = {
                'user_id': user_id,
                'message': message,
                'session_id': session_id,
                **kwargs
            }
            
            # 2. Perform safety checks
            safety_result = await self._check_safety(input_data)
            if not safety_result.passed:
                return {
                    'success': False,
                    'error': f"Safety check failed: {safety_result.reason}",
                    'session_id': session_id,
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': safety_result.metadata
                }
            
            # 3. Select the appropriate agent
            try:
                agent, context_update = self._select_agent(safety_result.sanitized_input or input_data)
            except Exception as e:
                return {
                    'success': False,
                    'error': f"Failed to select agent: {str(e)}",
                    'session_id': session_id,
                    'timestamp': datetime.utcnow().isoformat()
                }
            
            # 4. Process with the selected agent
            context = {
                'user_id': user_id,
                'session_id': session_id,
                'message': message,
                **kwargs,
                **context_update,
                'safety_metadata': safety_result.metadata
            }
            
            result = await self._with_retry(
                self._process_with_agent,
                agent,
                message,
                context
            )
            
            # 5. Add tracing and metrics
            if tracing_service and background_tasks:
                trace_data = {
                    'user_id': user_id,
                    'input': message,
                    'output': result.get('response', ''),
                    'agent_id': agent.name,
                    'safety_checks': safety_result.metadata,
                    'success': result.get('success', False)
                }
                background_tasks.add_task(
                    tracing_service.store_trace_async,
                    trace_data=trace_data,
                    session_id=session_id
                )
            
            return result
            
        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # Log the error for monitoring
            if tracing_service:
                await tracing_service.record_error({
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'traceback': str(e.__traceback__) if hasattr(e, '__traceback__') else None,
                    'context': {
                        'input': message,
                        'user_id': user_id,
                        **kwargs
                    },
                    'session_id': session_id,
                    'user_id': user_id
                })
            
            return {
                'success': False,
                'error': "An error occurred while processing your request",
                'session_id': session_id,
                'timestamp': datetime.utcnow().isoformat(),
                'internal_error': str(e) if settings.DEBUG else None
            }
    
    async def _process_with_agent(
        self,
        agent: Agent,
        message: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process a message with the specified agent.
        
        Args:
            agent: The agent to process the message
            message: The message to process
            context: Additional context for the agent
            
        Returns:
            Dict containing the agent's response and metadata
        """
        session_id = context.get('session_id', str(uuid.uuid4()))
        session = self.get_session(session_id)
        agent_name = getattr(agent, 'name', 'unknown')
        trace_id = None
        
        try:
            # Use the trace_agent_run context manager for tracing
            with trace_agent_run(agent_name=agent_name, session_id=session_id) as (trace_id, update_trace):
                try:
                    # Process the message with the agent using Runner.run()
                    # The first argument is the agent, second is the input
                    response = await Runner.run(agent, message, session=session)
                    
                    # Extract the response text
                    response_text = response.final_output if hasattr(response, 'final_output') else str(response)
                    
                    # Update the trace with the successful response
                    update_trace(
                        status='completed',
                        response=response_text,
                        metadata={
                            'agent_name': agent_name,
                            'session_id': session_id,
                            'timestamp': datetime.utcnow().isoformat()
                        }
                    )
                    
                    # Format the response
                    return {
                        'success': True,
                        'response': response_text,
                        'session_id': session_id,
                        'timestamp': datetime.utcnow().isoformat(),
                        'agent_type': agent_name,
                        'trace_id': trace_id
                    }
                    
                except Exception as e:
                    error_msg = f"Error processing message with agent: {str(e)}"
                    logger.error(error_msg, exc_info=True)
                    
                    # Update the trace with the error
                    update_trace(
                        status='error',
                        error=error_msg,
                        metadata={
                            'agent_name': agent_name,
                            'session_id': session_id,
                            'timestamp': datetime.utcnow().isoformat(),
                            'error_details': str(e)
                        }
                    )
                    
                    # Re-raise the exception to be handled by the outer try/except
                    raise
                    
        except Exception as e:
            error_msg = f"Error in agent processing pipeline: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # Log the error to tracing service if available
            if tracing_service:
                await tracing_service.record_error(
                    error=error_msg,
                    metadata={
                        'agent_type': agent_name,
                        'session_id': session_id,
                        'trace_id': trace_id,
                        'exception_type': type(e).__name__,
                        'stack_trace': str(e)
                    },
                    session_id=session_id
                )
                
            return {
                'success': False,
                'error': error_msg,
                'session_id': session_id,
                'timestamp': datetime.utcnow().isoformat(),
                'trace_id': trace_id
            }

    async def _with_retry(
        self,
        func: Callable[..., Awaitable[Dict[str, Any]]],
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute a function with retry logic.
            
        Args:
            func: The async function to execute
            *args: Positional arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
                
        Returns:
            The result of the function call
                
        Raises:
            Exception: If all retry attempts fail
        """
        last_error = None
        for attempt in range(self._max_retries):
            try:
                return await func(*args, **kwargs)
            except APIError as e:
                last_error = e
                if e.status_code == 429:  # Rate limit
                    wait_time = (2 ** attempt) * self._retry_delay
                    logger.warning(f"Rate limited. Waiting {wait_time} seconds before retry...")
                    await asyncio.sleep(wait_time)
                else:
                    raise
            except Exception as e:
                last_error = e
                if attempt == self._max_retries - 1:
                    break
                wait_time = (2 ** attempt) * self._retry_delay
                logger.warning(f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
        
        # If we get here, all retries failed
        error_msg = f"Failed after {self._max_retries} attempts. Last error: {str(last_error)}"
        logger.error(error_msg)
        raise Exception(error_msg)

    # Message handling methods
    async def add_message(
        self,
        agent_id: str,
        content: str,
        role: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Add a message to the conversation
        
        Args:
            agent_id: ID of the agent
            content: Message content
            role: Role of the message sender ('user' or 'assistant')
            metadata: Optional metadata
            
        Returns:
            Created message data
        """
        return await database_service.create_message(
            agent_id=agent_id,
            content=content,
            role=role,
            metadata=metadata or {}
        )
    
    async def get_messages(
        self,
        agent_id: str,
        limit: int = 10,
        cursor: str = None
    ) -> Dict[str, Any]:
        """
        Get messages for an agent
        
        Args:
            agent_id: ID of the agent
            limit: Maximum number of messages to return
            cursor: Cursor for pagination
            
        Returns:
            Dict containing messages and pagination info
        """
        return await database_service.get_messages(agent_id, limit, cursor)
    
    # Session management methods
    async def create_session(self, user_id: str, expires_at: datetime) -> Dict[str, Any]:
        """
        Create a new user session
        
        Args:
            user_id: ID of the user
            expires_at: Expiration datetime
            
        Returns:
            Session data
        """
        return await database_service.create_session(user_id, expires_at)
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a session by ID
        
        Args:
            session_id: ID of the session
            
        Returns:
            Session data if found, None otherwise
        """
        return await database_service.get_session(session_id)
    
    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a session
        
        Args:
            session_id: ID of the session to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        return await database_service.delete_session(session_id)

# Global agent service instance
agent_service = AgentService()
