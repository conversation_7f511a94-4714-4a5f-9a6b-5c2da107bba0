import asyncio
from typing import Dict, Any, Optional, List

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status

from app.core.security import verify_supabase_jwt
from app.schemas.agent import Agent<PERSON><PERSON>
from .agent_db_service import AgentDBService


class AgentManagementService:
    """
    Service for managing agent CRUD operations.
    """

    def __init__(self):
        self._agent_id_cache: Dict[str, str] = {}
        self._cache_lock = asyncio.Lock()

    async def create_agent(self, user_id: str, name: str, description: str = None, config: dict = None, access_token: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new agent.
        """
        db_service = AgentDBService(access_token=access_token)
        agent = await db_service.create_agent(
            user_id=user_id,
            agent_data=AgentCreate(
                name=name, description=description, config=config or {})
        )
        return agent.dict()

    async def get_agent(self, agent_id: str, access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get an agent by ID.
        """
        db_service = AgentDBService(access_token=access_token)
        agent = await db_service.get_agent(agent_id)
        return agent.dict() if agent else None

    async def get_user_agents(self, user_id: str, access_token: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all agents for a user.
        """
        db_service = AgentDBService(access_token=access_token)
        agents = await db_service.get_user_agents(user_id)
        return [agent.dict() for agent in agents]

    def get_available_agents(self) -> Dict[str, Any]:
        """
        Get information about available agents.
        """
        # This can be expanded to dynamically load agent information
        return {
            "hotel_manager": {
                "name": "HM Hotel Manager",
                "description": "Manages hotel-related tasks.",
                "capabilities": ["booking", "pricing", "information"],
            },
            "form_response": {
                "name": "Smart Response Agent",
                "description": "Handles form responses.",
                "capabilities": ["form_filling", "data_extraction"],
            }
        }

    async def get_agent_id(self, agent_name: str, access_token: Optional[str] = None) -> str:
        """Get agent ID from cache or database, creating it if it doesn't exist."""
        async with self._cache_lock:
            if agent_name in self._agent_id_cache:
                return self._agent_id_cache[agent_name]

        db_service = AgentDBService(access_token=access_token)
        db_agent = await db_service.get_agent_by_name(agent_name)

        if db_agent:
            agent_id = str(db_agent.id)
            async with self._cache_lock:
                self._agent_id_cache[agent_name] = agent_id
            return agent_id

        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Cannot create an agent without a valid user session."
            )

        try:
            user_payload = await verify_supabase_jwt(access_token)
            user_id = user_payload.get("sub")
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: user ID not found."
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Token verification failed: {str(e)}"
            )

        new_agent_data = AgentCreate(
            name=agent_name, description=f"System-created agent: {agent_name}")
        created_agent = await db_service.create_agent(user_id=user_id, agent_data=new_agent_data)

        agent_id = str(created_agent.id)
        async with self._cache_lock:
            self._agent_id_cache[agent_name] = agent_id
        return agent_id


agent_management_service = AgentManagementService()
