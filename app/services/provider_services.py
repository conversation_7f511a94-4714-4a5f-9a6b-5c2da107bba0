import logging
import httpx
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime, date

from app.schemas.integration import BookingComConfig, ExpediaConfig, AirbnbConfig

logger = logging.getLogger(__name__)


class BaseProviderService(ABC):
    """Base class for all provider integration services."""
    
    def __init__(self, credentials: Dict[str, Any], configuration: Dict[str, Any]):
        self.credentials = credentials
        self.configuration = configuration
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to the provider API."""
        pass
    
    @abstractmethod
    async def update_rates(self, room_type_id: str, date_from: str, date_to: str, rate: float, currency: str = "USD") -> bool:
        """Update rates for a room type."""
        pass
    
    @abstractmethod
    async def update_availability(self, room_type_id: str, date: str, available_rooms: int) -> bool:
        """Update availability for a room type."""
        pass
    
    @abstractmethod
    async def full_sync(self) -> bool:
        """Perform full synchronization."""
        pass


class BookingComService(BaseProviderService):
    """Booking.com integration service."""
    
    def __init__(self, credentials: Dict[str, Any], configuration: Dict[str, Any]):
        super().__init__(credentials, configuration)
        self.api_key = credentials.get("api_key")
        self.property_id = credentials.get("property_id")
        self.base_url = "https://distribution-xml.booking.com/2.0"
        
        # Validate configuration
        config = BookingComConfig(**configuration)
        self.sync_rates = config.sync_rates
        self.sync_availability = config.sync_availability
        self.sync_inventory = config.sync_inventory
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to Booking.com API."""
        try:
            # Mock API call for testing
            # In real implementation, make actual API call to test endpoint
            url = f"{self.base_url}/property/{self.property_id}/info"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Simulate API response
            await asyncio.sleep(0.5)  # Simulate network delay
            
            return {
                "success": True,
                "property_found": True,
                "api_access": True,
                "property_id": self.property_id,
                "last_tested": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Booking.com connection test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "last_tested": datetime.utcnow().isoformat()
            }
    
    async def update_rates(self, room_type_id: str, date_from: str, date_to: str, rate: float, currency: str = "USD") -> bool:
        """Update rates in Booking.com."""
        try:
            if not self.sync_rates:
                logger.info("Rate sync disabled for this Booking.com integration")
                return True
            
            # Prepare rate update payload
            payload = {
                "property_id": self.property_id,
                "room_type_id": room_type_id,
                "date_from": date_from,
                "date_to": date_to,
                "rate": rate,
                "currency": currency
            }
            
            # Mock API call
            logger.info(f"Updating Booking.com rates: {payload}")
            
            # In real implementation:
            # url = f"{self.base_url}/property/{self.property_id}/rates"
            # headers = {"Authorization": f"Bearer {self.api_key}"}
            # response = await self.client.post(url, json=payload, headers=headers)
            # return response.status_code == 200
            
            # Simulate processing time
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            logger.error(f"Booking.com rate update failed: {e}")
            return False
    
    async def update_availability(self, room_type_id: str, date: str, available_rooms: int) -> bool:
        """Update availability in Booking.com."""
        try:
            if not self.sync_availability:
                logger.info("Availability sync disabled for this Booking.com integration")
                return True
            
            # Prepare availability update payload
            payload = {
                "property_id": self.property_id,
                "room_type_id": room_type_id,
                "date": date,
                "available_rooms": available_rooms
            }
            
            # Mock API call
            logger.info(f"Updating Booking.com availability: {payload}")
            
            # Simulate processing time
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            logger.error(f"Booking.com availability update failed: {e}")
            return False
    
    async def full_sync(self) -> bool:
        """Perform full sync with Booking.com."""
        try:
            logger.info(f"Starting full sync with Booking.com for property {self.property_id}")
            
            # In real implementation, this would:
            # 1. Fetch all room types and rates from local system
            # 2. Push all data to Booking.com
            # 3. Optionally pull data from Booking.com to verify
            
            # Simulate longer processing time
            await asyncio.sleep(3)
            return True
            
        except Exception as e:
            logger.error(f"Booking.com full sync failed: {e}")
            return False


class ExpediaService(BaseProviderService):
    """Expedia integration service."""
    
    def __init__(self, credentials: Dict[str, Any], configuration: Dict[str, Any]):
        super().__init__(credentials, configuration)
        self.client_id = credentials.get("client_id")
        self.client_secret = credentials.get("client_secret")
        self.property_id = credentials.get("property_id")
        self.base_url = "https://services.expediapartnercentral.com/eqc/ar"
        
        # Validate configuration
        config = ExpediaConfig(**configuration)
        self.sync_rates = config.sync_rates
        self.sync_availability = config.sync_availability
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to Expedia API."""
        try:
            # Mock OAuth token exchange and property verification
            logger.info(f"Testing Expedia connection for property {self.property_id}")
            
            # Simulate API response
            await asyncio.sleep(0.5)
            
            return {
                "success": True,
                "property_found": True,
                "api_access": True,
                "property_id": self.property_id,
                "last_tested": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Expedia connection test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "last_tested": datetime.utcnow().isoformat()
            }
    
    async def update_rates(self, room_type_id: str, date_from: str, date_to: str, rate: float, currency: str = "USD") -> bool:
        """Update rates in Expedia."""
        try:
            if not self.sync_rates:
                logger.info("Rate sync disabled for this Expedia integration")
                return True
            
            # Prepare rate update payload
            payload = {
                "property_id": self.property_id,
                "room_type_id": room_type_id,
                "date_from": date_from,
                "date_to": date_to,
                "rate": rate,
                "currency": currency
            }
            
            logger.info(f"Updating Expedia rates: {payload}")
            
            # Simulate processing time
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            logger.error(f"Expedia rate update failed: {e}")
            return False
    
    async def update_availability(self, room_type_id: str, date: str, available_rooms: int) -> bool:
        """Update availability in Expedia."""
        try:
            if not self.sync_availability:
                logger.info("Availability sync disabled for this Expedia integration")
                return True
            
            # Prepare availability update payload
            payload = {
                "property_id": self.property_id,
                "room_type_id": room_type_id,
                "date": date,
                "available_rooms": available_rooms
            }
            
            logger.info(f"Updating Expedia availability: {payload}")
            
            # Simulate processing time
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            logger.error(f"Expedia availability update failed: {e}")
            return False
    
    async def full_sync(self) -> bool:
        """Perform full sync with Expedia."""
        try:
            logger.info(f"Starting full sync with Expedia for property {self.property_id}")
            
            # Simulate processing time
            await asyncio.sleep(3)
            return True
            
        except Exception as e:
            logger.error(f"Expedia full sync failed: {e}")
            return False


class AirbnbService(BaseProviderService):
    """Airbnb integration service."""
    
    def __init__(self, credentials: Dict[str, Any], configuration: Dict[str, Any]):
        super().__init__(credentials, configuration)
        self.access_token = credentials.get("access_token")
        self.listing_id = credentials.get("listing_id")
        self.base_url = "https://api.airbnb.com/v2"
        
        # Validate configuration
        config = AirbnbConfig(**configuration)
        self.sync_rates = config.sync_rates
        self.sync_availability = config.sync_availability
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to Airbnb API."""
        try:
            logger.info(f"Testing Airbnb connection for listing {self.listing_id}")
            
            # Simulate API response
            await asyncio.sleep(0.5)
            
            return {
                "success": True,
                "property_found": True,
                "api_access": True,
                "listing_id": self.listing_id,
                "last_tested": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Airbnb connection test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "last_tested": datetime.utcnow().isoformat()
            }
    
    async def update_rates(self, room_type_id: str, date_from: str, date_to: str, rate: float, currency: str = "USD") -> bool:
        """Update rates in Airbnb."""
        try:
            if not self.sync_rates:
                logger.info("Rate sync disabled for this Airbnb integration")
                return True
            
            # Prepare rate update payload
            payload = {
                "listing_id": self.listing_id,
                "date_from": date_from,
                "date_to": date_to,
                "rate": rate,
                "currency": currency
            }
            
            logger.info(f"Updating Airbnb rates: {payload}")
            
            # Simulate processing time
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            logger.error(f"Airbnb rate update failed: {e}")
            return False
    
    async def update_availability(self, room_type_id: str, date: str, available_rooms: int) -> bool:
        """Update availability in Airbnb."""
        try:
            if not self.sync_availability:
                logger.info("Availability sync disabled for this Airbnb integration")
                return True
            
            # For Airbnb, availability is typically boolean (available/blocked)
            is_available = available_rooms > 0
            
            payload = {
                "listing_id": self.listing_id,
                "date": date,
                "available": is_available
            }
            
            logger.info(f"Updating Airbnb availability: {payload}")
            
            # Simulate processing time
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            logger.error(f"Airbnb availability update failed: {e}")
            return False
    
    async def full_sync(self) -> bool:
        """Perform full sync with Airbnb."""
        try:
            logger.info(f"Starting full sync with Airbnb for listing {self.listing_id}")
            
            # Simulate processing time
            await asyncio.sleep(3)
            return True
            
        except Exception as e:
            logger.error(f"Airbnb full sync failed: {e}")
            return False


class ProviderServiceFactory:
    """Factory for creating provider service instances."""
    
    @staticmethod
    def create_service(provider_name: str, credentials: Dict[str, Any], configuration: Dict[str, Any]) -> BaseProviderService:
        """Create a provider service instance."""
        if provider_name == "booking_com":
            return BookingComService(credentials, configuration)
        elif provider_name == "expedia":
            return ExpediaService(credentials, configuration)
        elif provider_name == "airbnb":
            return AirbnbService(credentials, configuration)
        else:
            raise ValueError(f"Unsupported provider: {provider_name}")


# Add missing import
import asyncio
