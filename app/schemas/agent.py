from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, List, Dict, Any, Union
from enum import Enum

# Enums


class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class AgentType(str, Enum):
    GENERAL = "general"
    HOTEL_MANAGER = "hotel_manager"
    FORM_RESPONSE = "form_response"

# Request/Response Models


class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1, max_length=1000,
                         description="User message")
    session_id: Optional[str] = Field(
        None, description="Session ID for conversation memory")
    agent_type: Optional[AgentType] = Field(
        AgentType.GENERAL, description="Type of agent to use")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata for the message")


class ChatResponse(BaseModel):
    response: Union[str, Dict[str, Any]
                    ] = Field(..., description="Agent response (string or object)")
    session_id: str = Field(..., description="Session ID")
    agent_type: AgentType = Field(..., description="Type of agent used")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata")

# Agent Models


class AgentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100,
                      description="Agent name")
    description: Optional[str] = Field(
        None, max_length=500, description="Agent description")
    config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Agent configuration")


class AgentCreate(AgentBase):
    pass


class AgentUpdate(AgentBase):
    name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Agent name")


class Agent(AgentBase):
    id: str = Field(..., description="Unique identifier for the agent")
    user_id: str = Field(..., description="ID of the user who owns this agent")
    created_at: datetime = Field(..., description="When the agent was created")
    updated_at: datetime = Field(...,
                                 description="When the agent was last updated")

    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AgentInDB(Agent):
    """Agent model with database-specific fields"""
    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AgentInfo(BaseModel):
    name: str = Field(..., description="Agent name")
    description: str = Field(..., description="Agent description")
    capabilities: List[str] = Field(...,
                                    description="List of agent capabilities")

# Message Models


class MessageBase(BaseModel):
    content: str = Field(..., description="The message content")
    role: MessageRole = Field(...,
                              description="The role of the message sender")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata")


class MessageCreate(MessageBase):
    user_id: str = Field(...,
                         description="ID of the user who created this message")
    agent_id: str = Field(...,
                          description="ID of the agent this message belongs to")


class Message(MessageBase):
    id: str = Field(..., description="Unique identifier for the message")
    user_id: str = Field(...,
                         description="ID of the user who created this message")
    agent_id: str = Field(...,
                          description="ID of the agent this message belongs to")
    created_at: datetime = Field(...,
                                 description="When the message was created")
    updated_at: datetime = Field(...,
                                 description="When the message was last updated")

    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MessageInDB(Message):
    """Message model with database-specific fields"""
    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Session Models


class SessionBase(BaseModel):
    user_id: str = Field(...,
                         description="ID of the user who owns this session")
    expires_at: datetime = Field(..., description="When the session expires")


class SessionCreate(SessionBase):
    pass


class Session(SessionBase):
    id: str = Field(..., description="Unique identifier for the session")
    created_at: datetime = Field(...,
                                 description="When the session was created")

    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SessionInDB(Session):
    """Session model with database-specific fields"""
    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SessionInfo(Session):
    message_count: int = Field(
        0, description="Number of messages in the session")
    last_activity: Optional[datetime] = Field(
        None, description="Last activity timestamp")

# Pagination Models


class PaginationParams(BaseModel):
    limit: int = Field(
        10, ge=1, le=100, description="Number of items per page")
    cursor: Optional[str] = Field(None, description="Cursor for pagination")


class PaginatedMessages(BaseModel):
    messages: List[Message] = Field(..., description="List of messages")
    has_more: bool = Field(...,
                           description="Whether there are more messages available")
    next_cursor: Optional[str] = Field(
        None, description="Cursor for the next page of messages")

# Trace Models


class TraceBase(BaseModel):
    session_id: str = Field(...,
                            description="ID of the session this trace belongs to")
    user_id: Optional[str] = Field(None, description="ID of the user")
    agent_type: str = Field(
        "unknown", description="Type of agent that generated this trace")
    status: str = Field("completed", description="Status of the trace")
    user_message: Optional[str] = Field(
        None, description="User message that triggered this trace")
    ai_response: Optional[Dict[str, Any]] = Field(
        None, description="AI response data")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(
        None, description="Tool calls made during execution")
    trace_data: Optional[Dict[str, Any]] = Field(
        None, description="Full trace data")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata")
    duration_seconds: Optional[float] = Field(
        None, description="Duration of execution in seconds")
    start_time: Optional[datetime] = Field(
        None, description="Start time of execution")
    end_time: Optional[datetime] = Field(
        None, description="End time of execution")


class TraceCreate(TraceBase):
    pass


class Trace(TraceBase):
    id: str = Field(..., description="Unique identifier for the trace")
    created_at: datetime = Field(..., description="When the trace was created")
    updated_at: datetime = Field(...,
                                 description="When the trace was last updated")

    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
