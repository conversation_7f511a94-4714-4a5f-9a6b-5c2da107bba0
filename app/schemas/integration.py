from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum

# Enums for integration system
class AuthType(str, Enum):
    API_KEY = "api_key"
    OAUTH = "oauth"
    BASIC_AUTH = "basic_auth"

class SyncStatus(str, Enum):
    PENDING = "pending"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"

class OperationType(str, Enum):
    RATE_UPDATE = "rate_update"
    AVAILABILITY_UPDATE = "availability_update"
    INVENTORY_SYNC = "inventory_sync"
    FULL_SYNC = "full_sync"

class OperationStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"

class Direction(str, Enum):
    OUTBOUND = "outbound"
    INBOUND = "inbound"

# Integration Provider Schemas
class IntegrationProviderBase(BaseModel):
    name: str = Field(..., description="Provider identifier (e.g., 'booking_com', 'expedia')")
    display_name: str = Field(..., description="Human-readable provider name")
    api_base_url: Optional[str] = Field(None, description="Base URL for provider API")
    auth_type: AuthType = Field(..., description="Authentication type required")
    is_active: bool = Field(True, description="Whether provider is currently active")

class IntegrationProviderCreate(IntegrationProviderBase):
    pass

class IntegrationProvider(IntegrationProviderBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Hotel Integration Configuration Schemas
class HotelIntegrationBase(BaseModel):
    hotel_id: UUID = Field(..., description="Reference to hotel")
    provider_id: UUID = Field(..., description="Reference to integration provider")
    is_enabled: bool = Field(False, description="Whether integration is enabled")
    credentials: Dict[str, Any] = Field(default_factory=dict, description="Encrypted API keys, tokens")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Provider-specific settings")

class HotelIntegrationCreate(HotelIntegrationBase):
    pass

class HotelIntegrationUpdate(BaseModel):
    is_enabled: Optional[bool] = None
    credentials: Optional[Dict[str, Any]] = None
    configuration: Optional[Dict[str, Any]] = None

class HotelIntegration(HotelIntegrationBase):
    id: UUID
    last_sync_at: Optional[datetime] = None
    sync_status: SyncStatus = SyncStatus.PENDING
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class HotelIntegrationWithProvider(HotelIntegration):
    provider: IntegrationProvider

# Sync Operation Schemas
class SyncOperationBase(BaseModel):
    hotel_integration_id: UUID = Field(..., description="Reference to hotel integration")
    operation_type: OperationType = Field(..., description="Type of sync operation")
    direction: Direction = Field(..., description="Direction of sync")
    data_payload: Dict[str, Any] = Field(default_factory=dict, description="Operation data")
    external_reference: Optional[str] = Field(None, description="External system reference")

class SyncOperationCreate(SyncOperationBase):
    scheduled_at: Optional[datetime] = None

class SyncOperation(SyncOperationBase):
    id: UUID
    status: OperationStatus = OperationStatus.PENDING
    error_details: Optional[Dict[str, Any]] = None
    retry_count: int = 0
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True

# Sync Queue Schemas
class SyncQueueBase(BaseModel):
    hotel_id: UUID = Field(..., description="Reference to hotel")
    operation_type: OperationType = Field(..., description="Type of operation")
    priority: int = Field(5, description="Priority level (1-10, 1 being highest)")
    data: Dict[str, Any] = Field(default_factory=dict, description="Operation data")

class SyncQueueCreate(SyncQueueBase):
    scheduled_for: Optional[datetime] = None

class SyncQueueItem(SyncQueueBase):
    id: UUID
    status: OperationStatus = OperationStatus.PENDING
    scheduled_for: datetime
    attempts: int = 0
    max_attempts: int = 3
    created_at: datetime

    class Config:
        from_attributes = True

# API Request/Response Schemas
class RateUpdateRequest(BaseModel):
    room_type_id: UUID = Field(..., description="Room type to update")
    date_from: str = Field(..., description="Start date (YYYY-MM-DD)")
    date_to: str = Field(..., description="End date (YYYY-MM-DD)")
    new_rate: float = Field(..., description="New rate amount")
    currency: str = Field("USD", description="Currency code")
    providers: Optional[List[str]] = Field(None, description="Specific providers to sync to")

class AvailabilityUpdateRequest(BaseModel):
    room_type_id: UUID = Field(..., description="Room type to update")
    date: str = Field(..., description="Date (YYYY-MM-DD)")
    available_rooms: int = Field(..., description="Number of available rooms")
    providers: Optional[List[str]] = Field(None, description="Specific providers to sync to")

class BulkSyncOperation(BaseModel):
    type: OperationType = Field(..., description="Operation type")
    room_type_id: UUID = Field(..., description="Room type ID")
    date_from: Optional[str] = Field(None, description="Start date for rate updates")
    date_to: Optional[str] = Field(None, description="End date for rate updates")
    date: Optional[str] = Field(None, description="Specific date for availability updates")
    rate: Optional[float] = Field(None, description="Rate for rate updates")
    available_rooms: Optional[int] = Field(None, description="Available rooms for availability updates")

class BulkSyncRequest(BaseModel):
    operations: List[BulkSyncOperation] = Field(..., description="List of operations to perform")

# Integration Test Response
class IntegrationTestResponse(BaseModel):
    success: bool = Field(..., description="Whether test was successful")
    message: str = Field(..., description="Test result message")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional test details")

# Sync Status Response
class SyncStatusResponse(BaseModel):
    operation_id: UUID = Field(..., description="Sync operation ID")
    status: OperationStatus = Field(..., description="Current status")
    message: str = Field(..., description="Status message")
    progress: Optional[int] = Field(None, description="Progress percentage (0-100)")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Error details if failed")

# Provider Configuration Schemas
class BookingComConfig(BaseModel):
    api_key: str = Field(..., description="Booking.com API key")
    property_id: str = Field(..., description="Property ID in Booking.com")
    sync_rates: bool = Field(True, description="Enable rate synchronization")
    sync_availability: bool = Field(True, description="Enable availability synchronization")
    sync_inventory: bool = Field(False, description="Enable inventory synchronization")

class ExpediaConfig(BaseModel):
    client_id: str = Field(..., description="Expedia client ID")
    client_secret: str = Field(..., description="Expedia client secret")
    property_id: str = Field(..., description="Property ID in Expedia")
    sync_rates: bool = Field(True, description="Enable rate synchronization")
    sync_availability: bool = Field(True, description="Enable availability synchronization")

class AirbnbConfig(BaseModel):
    access_token: str = Field(..., description="Airbnb access token")
    listing_id: str = Field(..., description="Listing ID in Airbnb")
    sync_rates: bool = Field(True, description="Enable rate synchronization")
    sync_availability: bool = Field(True, description="Enable availability synchronization")
