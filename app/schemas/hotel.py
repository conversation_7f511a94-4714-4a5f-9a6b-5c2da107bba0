from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from uuid import UUID

class RoomTypeBase(BaseModel):
    name: str
    description: Optional[str] = None
    max_occupancy: int
    base_price: float
    amenities: Optional[str] = None

class RoomTypeCreate(RoomTypeBase):
    pass

class RoomType(RoomTypeBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class RoomBase(BaseModel):
    room_number: str
    floor: int
    is_available: bool = True

class RoomCreate(RoomBase):
    hotel_id: UUID
    room_type_id: UUID

class Room(RoomBase):
    id: UUID
    hotel_id: UUID
    room_type_id: UUID
    is_clean: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class RoomWithType(Room):
    room_type: RoomType

class HotelBase(BaseModel):
    name: str
    location: str
    description: Optional[str] = None

class HotelCreate(HotelBase):
    pass

class Hotel(HotelBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class HotelWithRooms(Hotel):
    rooms: List[RoomWithType] = []

class RoomAvailability(BaseModel):
    room_type: RoomType
    available_rooms: int
    price_per_night: float
