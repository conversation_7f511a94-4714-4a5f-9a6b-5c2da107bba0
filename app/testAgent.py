import asyncio
from agents import Agent, Runner
from agents.tracing import set_tracing_disabled
from app.core.openai_config import azure_openai_client


async def main():
    # Disable tracing to prevent API key errors
    set_tracing_disabled(True)

    # Initialize the Azure OpenAI client
    await azure_openai_client.initialize()

    # Create an agent
    agent = Agent(
        name="TestAgent",
        instructions="You are a helpful assistant that can write haikus.",
    )

    # Run the agent
    result = await Runner.run(agent, "Write a haiku about asynchronous programming.")

    # Print the result
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
