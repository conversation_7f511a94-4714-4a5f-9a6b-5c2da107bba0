from app.core.supabase_client import supabase

def get_hotel(hotel_id: str):
    """Get a single hotel by ID"""
    response = supabase.table("hotels").select("*").eq("id", hotel_id).execute()
    return response.data[0] if response.data else None

def get_hotels(skip: int = 0, limit: int = 100):
    """Get list of hotels with pagination"""
    response = supabase.table("hotels").select("*").range(skip, skip + limit - 1).execute()
    return response.data

def create_hotel(name: str, location: str, description: str = None):
    """Create a new hotel"""
    response = supabase.table("hotels").insert({
        "name": name,
        "location": location,
        "description": description
    }).execute()
    return response.data[0] if response.data else None

def update_hotel(hotel_id: str, **kwargs):
    """Update hotel details"""
    response = supabase.table("hotels").update(kwargs).eq("id", hotel_id).execute()
    return response.data[0] if response.data else None

def delete_hotel(hotel_id: str):
    """Delete a hotel"""
    response = supabase.table("hotels").delete().eq("id", hotel_id).execute()
    return True if response.data else False
