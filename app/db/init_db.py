from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.db.models import Base, Hotel, RoomType, Room
from app.core.config import settings
import os

def init_db():
    # Ensure the database directory exists
    db_dir = os.path.dirname(os.path.abspath(settings.DATABASE_URL.replace("sqlite:///", "")))
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
    
    # Create the database engine with SQLite-specific parameters
    engine = create_engine(
        settings.DATABASE_URL,
        connect_args={"check_same_thread": False} if settings.DATABASE_URL.startswith("sqlite") else {}
    )
    
    # Drop all tables first (be careful with this in production!)
    print("Dropping existing tables...")
    Base.metadata.drop_all(bind=engine)
    
    # Create all tables
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created successfully!")
    
    return engine

def seed_data(engine):
    # Create a new session
    Session = sessionmaker(bind=engine)
    db = Session()
    
    try:
        # Create sample room types
        room_types = [
            RoomType(
                name="Deluxe King",
                description="Spacious room with a king-size bed, city view, and luxury amenities.",
                max_occupancy=2,
                base_price=299.99,
                amenities="Free WiFi, Air Conditioning, Flat-screen TV, Minibar, Coffee Maker, Safe, Work Desk"
            ),
            RoomType(
                name="Executive Suite",
                description="Luxurious suite with separate living area, premium amenities, and city view.",
                max_occupancy=3,
                base_price=499.99,
                amenities="Free WiFi, Air Conditioning, 2x Flat-screen TVs, Minibar, Coffee Maker, Safe, Work Desk, Sofa Bed"
            )
        ]
        
        db.add_all(room_types)
        db.flush()  # Flush to get the IDs
        
        # Create sample hotels
        hotels = [
            Hotel(
                name="Grand Plaza Hotel",
                location="123 Downtown Ave, Metropolis",
                description="Luxury 5-star hotel in the heart of the city with world-class amenities and services.",
            ),
            Hotel(
                name="Seaside Resort & Spa",
                location="456 Ocean Drive, Coastal City",
                description="Beachfront resort offering stunning ocean views and premium spa services.",
            )
        ]
        
        db.add_all(hotels)
        db.flush()
        
        # Create some rooms for the first hotel
        rooms = [
            Room(
                hotel_id=hotels[0].id,
                room_type_id=room_types[0].id,
                room_number="101",
                floor=1,
                is_available=True,
                is_clean=True,
                needs_maintenance=False
            ),
            Room(
                hotel_id=hotels[0].id,
                room_type_id=room_types[1].id,
                room_number="201",
                floor=2,
                is_available=True,
                is_clean=True,
                needs_maintenance=False
            )
        ]
        
        db.add_all(rooms)
        db.commit()
        print("✅ Sample data seeded successfully!")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Error seeding data: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    print("Initializing database...")
    engine = init_db()
    print("\nSeeding sample data...")
    seed_data(engine)
