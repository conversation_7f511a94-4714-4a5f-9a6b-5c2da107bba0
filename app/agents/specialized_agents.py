"""
Specialized Agents for Hotel Management

This module implements the proper OpenAI Agents SDK pattern where each agent
has only the specific tools it needs, eliminating unnecessary initializations.

Architecture:
- Triage Agent: Routes user requests to specialist agents
- Hotel Listing Agent: Only handles hotel listing (list_hotels tool)
- Price Update Agent: Only handles price updates (returns JSON forms)
- Room Info Agent: Only handles room information queries

Each agent is lightweight and only initializes the tools it actually uses.
"""

import logging
from typing import Dict, Any, Optional
from agents import Agent
from app.core.config import settings

logger = logging.getLogger(__name__)

# Get the model name for Azure OpenAI
model_name = settings.AZURE_OPENAI_MODEL or "gpt-4o-mini"


class SpecializedAgentSystem:
    """Manages specialized agents with minimal tool loading."""

    def __init__(self):
        self._agents: Dict[str, Agent] = {}
        self._tools_cache: Dict[str, Any] = {}

    def _get_hotel_listing_agent(self) -> Agent:
        """Agent specialized for listing hotels - only loads list_hotels tool."""
        if "hotel_listing" in self._agents:
            return self._agents["hotel_listing"]

        # Import only the tool we need
        from app.agents.tools.hotel_tools import list_hotels

        agent = Agent(
            name="hotel_listing_specialist",
            instructions="""You are a hotel listing specialist. Your only job is to help users find and list available hotels.

CAPABILITIES:
- List all available hotels
- Filter hotels by location
- Provide basic hotel information

RESPONSE STYLE:
- Be concise and helpful
- Always use the list_hotels tool to get current data
- Format responses clearly with hotel names and locations""",
            tools=[list_hotels],  # Only one tool!
            model=model_name,
        )

        self._agents["hotel_listing"] = agent
        logger.info("Created specialized hotel listing agent with 1 tool")
        return agent

    def _get_price_update_agent(self) -> Agent:
        """Agent specialized for price updates - only loads form generation tool."""
        if "price_update" in self._agents:
            return self._agents["price_update"]

        # Import only the tool we need
        from app.agents.tools.hotel_tools import get_update_room_price_form

        agent = Agent(
            name="price_update_specialist",
            instructions="""You are a price update specialist. Your job is to generate forms for updating hotel room prices.

CRITICAL FORM HANDLING RULES - MANDATORY:
- When user asks for a form, says "give me form", "show me form", "I want form", or similar
- When user wants to update prices but hasn't provided hotel name, room type, or new price
- When user asks for a way to update prices
- YOU MUST use get_update_room_price_form tool
- YOU MUST return the JSON response exactly as provided by the tool
- DO NOT describe what the form contains
- DO NOT explain the form fields
- DO NOT add any text before or after the JSON
- JUST return the JSON response

RESPONSE FORMAT RULES:
- When using get_update_room_price_form, return the JSON response exactly as provided by the tool
- Do NOT wrap the JSON in additional text or descriptions
- The frontend expects the raw JSON form specification to render the form""",
            tools=[get_update_room_price_form],  # Only one tool!
            model=model_name,
        )

        self._agents["price_update"] = agent
        logger.info("Created specialized price update agent with 1 tool")
        return agent

    def _get_form_submission_agent(self) -> Agent:
        """Agent specialized for processing form submissions."""
        if "form_submission" in self._agents:
            return self._agents["form_submission"]

        # Import the tools needed for form processing
        from app.agents.tools.hotel_tools import update_room_price, get_room_prices

        agent = Agent(
            name="form_submission_processor",
            instructions="""You are a form submission processor. Your job is to process submitted form data and execute the requested actions.

FORM SUBMISSION PROCESSING RULES - MANDATORY:
- When you receive a "Form submitted:" message, extract the JSON data from it
- Parse the form data to understand what action needs to be taken
- For room price updates, use the update_room_price tool with the provided data
- Always confirm the action was completed successfully
- Provide clear feedback about what was updated

RESPONSE FORMAT:
- After processing a form submission, provide a clear confirmation message
- Include details about what was updated (hotel name, room type, new price, dates)
- If there's an error, explain what went wrong and what the user should do

Example form data processing:
- Extract hotel_name, room_type, new_price, start_date, end_date from the JSON
- Call update_room_price with these parameters
- Confirm the update was successful""",
            tools=[update_room_price, get_room_prices],
            model=model_name,
        )

        self._agents["form_submission"] = agent
        logger.info("Created specialized form submission agent with 2 tools")
        return agent

    def _get_clarification_agent(self) -> Agent:
        """Agent that handles ambiguous responses and asks for clarification."""
        if "clarification" in self._agents:
            return self._agents["clarification"]

        agent = Agent(
            name="clarification_specialist",
            instructions="""You are a clarification specialist. Your job is to help users when their responses are ambiguous or unclear.

WHEN TO USE:
- User gives vague responses like "yes", "please", "ok", "sure"
- User's intent is unclear from their message
- User needs guidance on what they can do

RESPONSE STYLE:
- Be helpful and friendly
- Ask specific questions to understand what the user wants
- Provide clear, actionable options
- Don't make assumptions about what they want

SPECIFIC SCENARIOS:
When user says "yes please" or similar after seeing capabilities:
- Ask them to choose a specific action
- Provide numbered options like:
  "Great! What would you like to do?
  1. See a list of available hotels
  2. Check room prices for a specific hotel
  3. Update room pricing
  4. Something else - just tell me what you need"

AVOID:
- Making assumptions about hotel names from examples
- Jumping to conclusions about what the user wants
- Mentioning specific hotels unless the user does first
- Providing information they didn't ask for""",
            tools=[],  # No tools needed for clarification
            model=model_name,
        )

        self._agents["clarification"] = agent
        logger.info("Created specialized clarification agent")
        return agent

    def _get_room_info_agent(self) -> Agent:
        """Agent specialized for room information - only loads room price tool."""
        if "room_info" in self._agents:
            return self._agents["room_info"]

        # Import only the tool we need
        from app.agents.tools.hotel_tools import get_room_prices

        agent = Agent(
            name="room_info_specialist",
            instructions="""You are a room information specialist. Your job is to provide detailed information about hotel rooms and their prices.

CAPABILITIES:
- Get current room prices and availability
- Show room details and features
- Provide pricing information for specific hotels

RESPONSE STYLE:
- Be detailed and informative
- Always use get_room_prices tool for current data
- Format responses clearly with prices and room details""",
            tools=[get_room_prices],  # Only one tool!
            model=model_name,
        )

        self._agents["room_info"] = agent
        logger.info("Created specialized room info agent with 1 tool")
        return agent

    def _get_triage_agent(self) -> Agent:
        """Triage agent that routes to specialist agents."""
        if "triage" in self._agents:
            return self._agents["triage"]

        # Get specialist agents for handoffs
        hotel_listing_agent = self._get_hotel_listing_agent()
        price_update_agent = self._get_price_update_agent()
        room_info_agent = self._get_room_info_agent()

        agent = Agent(
            name="hotel_triage",
            instructions="""You are a hotel management triage agent. Your job is to understand what the user wants and route them to the right specialist.

CRITICAL CONTEXT AWARENESS:
- NEVER assume hotel names from previous examples or conversations unless the user explicitly mentions them
- If user gives vague responses like "yes please", "ok", "sure" - ask for clarification about what they specifically want
- Don't make assumptions about what the user wants based on examples you've given

ROUTING RULES:
- If user wants to LIST HOTELS or see available hotels → Use hotel_listing_specialist
- If user wants to UPDATE PRICES or change room prices → Use price_update_specialist
- If user wants ROOM INFO, prices, or availability → Use room_info_specialist

IMPORTANT:
- Always hand off to the appropriate specialist
- Don't try to answer directly - let the specialists handle their domains
- Be quick and efficient in routing
- When in doubt, ask for clarification rather than making assumptions""",
            handoffs=[hotel_listing_agent,
                      price_update_agent, room_info_agent],
            model=model_name,
        )

        self._agents["triage"] = agent
        logger.info("Created triage agent with 3 handoff options")
        return agent

    def get_agent_for_intent(self, intent: str) -> Agent:
        """Get the appropriate agent based on user intent."""
        intent_lower = intent.lower()

        if "form_submission" in intent_lower:
            logger.info("Routing to form submission processor")
            return self._get_form_submission_agent()
        elif "clarification_needed" in intent_lower:
            logger.info("Routing to clarification specialist")
            return self._get_clarification_agent()
        elif "list" in intent_lower and "hotel" in intent_lower:
            logger.info("Routing to hotel listing specialist")
            return self._get_hotel_listing_agent()
        elif "update" in intent_lower and "price" in intent_lower:
            logger.info("Routing to price update specialist")
            return self._get_price_update_agent()
        elif "price" in intent_lower or "room" in intent_lower or "info" in intent_lower:
            logger.info("Routing to room info specialist")
            return self._get_room_info_agent()
        else:
            logger.info("Using triage agent for routing")
            return self._get_triage_agent()

    def analyze_user_message(self, message: str) -> str:
        """Analyze user message to determine intent."""
        message_lower = message.lower().strip()

        # Check for form submissions first (highest priority)
        if "form submitted:" in message_lower:
            return "form_submission"

        # Check for vague/ambiguous responses that need clarification
        if message_lower in ["yes", "yes please", "please", "ok", "okay", "sure", "go ahead", "continue"]:
            return "clarification_needed"

        # Direct intent detection
        if any(phrase in message_lower for phrase in ["list hotels", "show hotels", "available hotels", "what hotels"]):
            return "list_hotels"
        elif any(phrase in message_lower for phrase in [
            "update price", "change price", "modify price", "price update",
            "give me form", "show me form", "i want form", "form to update",
            "update room price", "price form", "form for price", "form for updating"
        ]):
            return "update_price"
        elif any(phrase in message_lower for phrase in ["room price", "room info", "room details", "how much", "cost"]):
            return "room_info"
        else:
            return "general"

    def get_fast_agent(self, message: str) -> Agent:
        """Get the fastest, most appropriate agent for the user's message."""
        intent = self.analyze_user_message(message)
        return self.get_agent_for_intent(intent)

    def clear_cache(self):
        """Clear agent cache for testing."""
        self._agents.clear()
        logger.info("Cleared specialized agent cache")

    def get_stats(self) -> Dict[str, Any]:
        """Get system statistics."""
        return {
            "cached_agents": len(self._agents),
            "available_specialists": ["hotel_listing", "price_update", "room_info", "triage"]
        }


# Create singleton instance
specialized_agent_system = SpecializedAgentSystem()


def get_specialized_agent(message: str) -> Agent:
    """
    Quick function to get the right specialized agent for a message.

    Args:
        message: User's message

    Returns:
        Agent: The most appropriate specialized agent
    """
    return specialized_agent_system.get_fast_agent(message)
