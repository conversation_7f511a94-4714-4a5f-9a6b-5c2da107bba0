"""
Safety and Guardrails Module

This module provides input validation, content moderation, and safety checks
for the agent system.
"""

import re
import logging
from typing import Dict, Any, Optional, Tuple
from pydantic import BaseModel, validator, HttpUrl
from datetime import datetime
import asyncio

from app.core.openai_config import azure_openai_client

logger = logging.getLogger(__name__)


class SafetyCheckResult(BaseModel):
    """Result of a safety check operation"""
    passed: bool
    reason: str = ""
    sanitized_input: Optional[Any] = None
    metadata: Dict[str, Any] = {}


class InputValidator:
    """Validates and sanitizes user inputs"""

    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    @staticmethod
    def validate_phone(phone: str) -> bool:
        """Validate phone number format"""
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', phone)
        # Check if it's a valid length (10 for NANP, 11 if country code included)
        return len(digits) in (10, 11)

    @staticmethod
    def sanitize_text(text: str) -> str:
        """Basic text sanitization"""
        # Remove potentially harmful characters
        sanitized = re.sub(r'[<>\"\'`]', '', text)
        # Trim whitespace
        return sanitized.strip()

    @classmethod
    def validate_input(cls, input_data: Dict[str, Any]) -> SafetyCheckResult:
        """
        Validate and sanitize input data

        Args:
            input_data: Dictionary containing user input data

        Returns:
            SafetyCheckResult with validation status
        """
        try:
            # Create a copy to avoid modifying the original
            sanitized = input_data.copy()

            # Check for required fields
            required_fields = ['user_id', 'message']
            for field in required_fields:
                if field not in input_data:
                    return SafetyCheckResult(
                        passed=False,
                        reason=f"Missing required field: {field}"
                    )

            # Sanitize text fields
            for field in ['message', 'name', 'email', 'phone']:
                if field in sanitized and sanitized[field]:
                    if isinstance(sanitized[field], str):
                        sanitized[field] = cls.sanitize_text(sanitized[field])

            # Validate email if present
            if 'email' in sanitized and sanitized['email']:
                if not cls.validate_email(sanitized['email']):
                    return SafetyCheckResult(
                        passed=False,
                        reason="Invalid email format"
                    )

            # Validate phone if present
            if 'phone' in sanitized and sanitized['phone']:
                if not cls.validate_phone(sanitized['phone']):
                    return SafetyCheckResult(
                        passed=False,
                        reason="Invalid phone number format"
                    )

            return SafetyCheckResult(
                passed=True,
                reason="Input validation passed",
                sanitized_input=sanitized,
                metadata={"validated_at": datetime.utcnow().isoformat()}
            )

        except Exception as e:
            logger.error(
                f"Error during input validation: {str(e)}", exc_info=True)
            return SafetyCheckResult(
                passed=False,
                reason=f"Validation error: {str(e)}"
            )


class LLMRelevanceChecker:
    """
    Uses an LLM to check if the user's message is relevant to the application's domain.
    """

    async def check_relevance(self, text: str) -> SafetyCheckResult:
        """
        Check if the message is relevant to the hotel management domain using an LLM.

        Args:
            text: The user's message.

        Returns:
            SafetyCheckResult indicating if the message is relevant.
        """
        if not text or not isinstance(text, str):
            return SafetyCheckResult(
                passed=True,
                reason="No text content to check for relevance"
            )

        try:
            prompt = f"""
            You are an AI guardrail. Your only function is to determine if a user's query is related to the hotel management domain.
            The hotel management domain includes topics like: hotels, rooms, bookings, reservations, availability, pricing, amenities, check-in/check-out, guest services, listings, and properties.

            Analyze the following user query and respond with a single word: RELEVANT or IRRELEVANT.
            Do not provide any explanation or additional text. Your entire response must be either RELEVANT or IRRELEVANT.

            User Query: "{text}"
            """

            response = await azure_openai_client.chat.completions.create(
                model=azure_openai_client.get_model_name(),
                messages=[{"role": "user", "content": prompt}],
                max_tokens=5,
                temperature=0.0,
                stop=None,
            )

            decision = response.choices[0].message.content.strip().upper()
            logger.debug(
                f"LLM relevance check for '{text[:50]}...': {decision}")

            if decision == "RELEVANT":
                return SafetyCheckResult(
                    passed=True,
                    reason="Message is relevant to the domain.",
                    metadata={"relevance_checked_at": datetime.utcnow(
                    ).isoformat(), "llm_decision": decision}
                )
            else:  # Default to irrelevant for any other response
                return SafetyCheckResult(
                    passed=False,
                    reason="The question does not seem to be related to our services. Please ask about hotel management or related topics.",
                    metadata={"relevance_checked_at": datetime.utcnow(
                    ).isoformat(), "llm_decision": decision}
                )

        except Exception as e:
            logger.error(
                f"Error during LLM relevance check: {str(e)}", exc_info=True)
            # Fail open to avoid blocking legitimate requests in case of an error
            return SafetyCheckResult(
                passed=True,
                reason=f"Relevance check error: {str(e)}",
                metadata={"error": str(e)}
            )


class ContentModerator:
    """Moderates content for inappropriate or sensitive information"""

    def __init__(self):
        # These would be loaded from a configuration or database in production
        self.banned_terms = [
            # Add sensitive or inappropriate terms here
            "credit card", "ssn", "social security", "password"
        ]
        self.sensitive_patterns = [
            # Regex patterns for sensitive data
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # Phone numbers
            r'\b\d{3}[-.]?\d{2}[-.]?\d{4}\b',  # SSN-like patterns
            r'\b\d{16}\b',  # 16-digit credit card numbers
            # 16-digit credit card with separators
            r'\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b',
        ]

    def check_content(self, text: str) -> SafetyCheckResult:
        """
        Check content for inappropriate or sensitive information

        Args:
            text: Text content to check

        Returns:
            SafetyCheckResult with moderation status
        """
        try:
            if not text or not isinstance(text, str):
                return SafetyCheckResult(
                    passed=True,
                    reason="No text content to moderate"
                )

            # Check for banned terms
            lower_text = text.lower()
            for term in self.banned_terms:
                if term in lower_text:
                    return SafetyCheckResult(
                        passed=False,
                        reason=f"Content contains restricted term: {term}",
                        metadata={"banned_term": term}
                    )

            # Check for sensitive patterns
            for pattern in self.sensitive_patterns:
                if re.search(pattern, text):
                    return SafetyCheckResult(
                        passed=False,
                        reason="Content may contain sensitive information",
                        metadata={"pattern_matched": pattern}
                    )

            return SafetyCheckResult(
                passed=True,
                reason="Content moderation passed",
                metadata={"moderated_at": datetime.utcnow().isoformat()}
            )

        except Exception as e:
            logger.error(
                f"Error during content moderation: {str(e)}", exc_info=True)
            # Fail open in case of error to avoid blocking legitimate requests
            return SafetyCheckResult(
                passed=True,
                reason=f"Moderation error: {str(e)}",
                metadata={"error": str(e)}
            )


# Singleton instances
input_validator = InputValidator()
content_moderator = ContentModerator()
relevance_checker = LLMRelevanceChecker()


async def check_input_safety(input_data: Dict[str, Any]) -> SafetyCheckResult:
    """
    Perform all safety checks on input data

    Args:
        input_data: User input data to check

    Returns:
        SafetyCheckResult with overall safety status
    """
    # 1. Validate and sanitize input
    validation_result = input_validator.validate_input(input_data)
    if not validation_result.passed:
        return validation_result

    # 2. Check message content for moderation issues
    sanitized_message = validation_result.sanitized_input.get('message')
    if sanitized_message:
        moderation_result = content_moderator.check_content(sanitized_message)
        if not moderation_result.passed:
            return moderation_result

    # 3. Check message for relevance
    if sanitized_message:
        relevance_result = await relevance_checker.check_relevance(sanitized_message)
        if not relevance_result.passed:
            return relevance_result

    return SafetyCheckResult(
        passed=True,
        reason="All safety checks passed",
        sanitized_input=validation_result.sanitized_input,
        metadata={
            "validated_at": datetime.utcnow().isoformat(),
            "checks_passed": ["input_validation", "content_moderation", "relevance_check"]
        }
    )
