"""
Action Tools Module

Provides tools for taking actions like making API requests to external services
and performing system operations.
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, HttpUrl
import httpx
import logging
import json

logger = logging.getLogger(__name__)

class ApiRequest(BaseModel):
    """Represents an API request"""
    method: str = Field(..., pattern="^(GET|POST|PUT|DELETE|PATCH)$")
    url: HttpUrl
    headers: Dict[str, str] = Field(default_factory=dict)
    params: Dict[str, Any] = Field(default_factory=dict)
    body: Optional[Union[Dict[str, Any], str]] = None
    timeout: int = 30

class ApiResponse(BaseModel):
    """Represents an API response"""
    status_code: int
    headers: Dict[str, str]
    content: Union[Dict[str, Any], str, bytes]
    is_success: bool
    error: Optional[str] = None

class ActionTool:
    """Tool for executing API requests and other actions"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.name = "execute_action"
        self.description = "Execute API requests and other actions"
        self.parameters = {
            "type": "object",
            "properties": {
                "action_type": {
                    "type": "string",
                    "enum": ["api_request", "database_query", "system_action"],
                    "description": "Type of action to execute"
                },
                "details": {
                    "type": "object",
                    "description": "Action-specific parameters"
                }
            },
            "required": ["action_type", "details"]
        }
        self._client = httpx.AsyncClient()
    
    async def __call__(self, action_type: str, details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an action based on the provided type and details
        
        Args:
            action_type: Type of action to execute
            details: Action-specific parameters
            
        Returns:
            Result of the action execution
        """
        try:
            if action_type == "api_request":
                return await self._execute_api_request(details)
            elif action_type == "database_query":
                return await self._execute_database_query(details)
            elif action_type == "system_action":
                return await self._execute_system_action(details)
            else:
                raise ValueError(f"Unknown action type: {action_type}")
                
        except Exception as e:
            logger.error(f"Error executing action: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    async def _execute_api_request(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Execute an HTTP API request"""
        try:
            # Parse and validate the request
            request = ApiRequest(**details)
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                **request.headers
            }
            
            # Make the request
            async with self._client as client:
                response = await client.request(
                    method=request.method,
                    url=str(request.url),
                    params=request.params,
                    json=request.body if isinstance(request.body, dict) else None,
                    data=json.dumps(request.body) if isinstance(request.body, dict) else request.body,
                    headers=headers,
                    timeout=request.timeout
                )
                
                # Parse response
                try:
                    content = response.json()
                except ValueError:
                    content = response.text
                
                api_response = ApiResponse(
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    content=content,
                    is_success=response.is_success
                )
                
                return {
                    "success": True,
                    "response": api_response.dict()
                }
                
        except httpx.RequestError as e:
            error_msg = f"Request failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "details": {"request": details}
            }
    
    async def _execute_database_query(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a database query"""
        # TODO: Implement database query execution
        # This would connect to the appropriate database and execute the query
        return {
            "success": False,
            "error": "Database query execution not implemented yet"
        }
    
    async def _execute_system_action(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a system-level action"""
        # TODO: Implement system action execution
        # This would handle actions like file operations, process management, etc.
        return {
            "success": False,
            "error": "System action execution not implemented yet"
        }

# Create a singleton instance
action_tool = ActionTool()
