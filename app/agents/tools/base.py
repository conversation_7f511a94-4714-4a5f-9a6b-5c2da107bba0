"""Base classes and utilities for agent tools.

This module provides a standardized way to define, register, and use tools
in the agent system. Tools are functions that can be called by the agent
to perform specific tasks or retrieve information.
"""
from typing import Any, Dict, List, Optional, Type, TypeVar, Callable, Awaitable, Union
from pydantic import BaseModel, Field
from functools import wraps
import inspect
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T', bound='BaseTool')

class ToolError(Exception):
    """Base exception for tool-related errors."""
    pass

class ValidationError(ToolError):
    """Raised when tool input validation fails."""
    pass

class ToolExecutionError(ToolError):
    """Raised when a tool fails during execution."""
    pass

class ToolRegistry:
    """Registry for managing available tools."""
    _instance = None
    _tools: Dict[str, 'BaseTool'] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ToolRegistry, cls).__new__(cls)
        return cls._instance
    
    @classmethod
    def register(cls, tool: 'BaseTool') -> 'BaseTool':
        """Register a tool in the registry."""
        if tool.name in cls._tools:
            logger.warning(f"Tool with name '{tool.name}' is already registered. Overwriting.")
        cls._tools[tool.name] = tool
        return tool
    
    @classmethod
    def get_tool(cls, name: str) -> Optional['BaseTool']:
        """Get a tool by name."""
        return cls._tools.get(name)
    
    @classmethod
    def get_tools(cls) -> Dict[str, 'BaseTool']:
        """Get all registered tools."""
        return cls._tools.copy()

class ToolInput(BaseModel):
    """Base class for tool input schemas."""
    pass

class ToolOutput(BaseModel):
    """Base class for tool output schemas."""
    success: bool = Field(..., description="Whether the tool execution was successful")
    result: Any = Field(None, description="The result of the tool execution")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class BaseTool:
    """Base class for all tools.
    
    Subclasses should implement the `_execute` method and define their own
    input and output schemas by setting the `input_schema` and `output_schema`
    class attributes.
    """
    name: str
    description: str
    input_schema: Type[ToolInput] = ToolInput
    output_schema: Type[ToolOutput] = ToolOutput
    
    def __init_subclass__(cls, **kwargs):
        """Register tool subclasses automatically."""
        super().__init_subclass__(**kwargs)
        if not inspect.isabstract(cls):
            ToolRegistry.register(cls())
    
    async def execute(self, **kwargs) -> ToolOutput:
        """Execute the tool with the given input."""
        try:
            # Validate input
            try:
                validated_input = self.input_schema(**kwargs)
            except Exception as e:
                raise ValidationError(f"Invalid input: {str(e)}")
            
            # Execute the tool
            try:
                result = await self._execute(validated_input)
                
                # Ensure the result is a ToolOutput
                if not isinstance(result, ToolOutput):
                    result = self.output_schema(
                        success=True,
                        result=result
                    )
                return result
                
            except Exception as e:
                logger.exception(f"Error executing tool {self.name}")
                return self.output_schema(
                    success=False,
                    error=str(e),
                    metadata={"error_type": type(e).__name__}
                )
                
        except Exception as e:
            logger.exception(f"Unexpected error in tool {self.name}")
            return self.output_schema(
                success=False,
                error=f"An unexpected error occurred: {str(e)}",
                metadata={"error_type": type(e).__name__}
            )
    
    async def _execute(self, input_data: ToolInput) -> Union[ToolOutput, Any]:
        """Execute the tool with validated input.
        
        Subclasses should implement this method to provide the tool's functionality.
        
        Args:
            input_data: Validated input data
            
        Returns:
            Either a ToolOutput instance or a raw result that will be wrapped in a ToolOutput
        """
        raise NotImplementedError("Subclasses must implement this method")

def tool(name: str = None, description: str = None):
    """Decorator to register a function as a tool.
    
    Example:
        @tool(name="get_weather", description="Get the current weather for a location")
        async def get_weather(location: str) -> str:
            return f"The weather in {location} is sunny"
    """
    def decorator(func):
        # Create a tool class dynamically
        tool_name = name or func.__name__
        tool_description = description or func.__doc__ or ""
        
        # Create input schema from function signature
        sig = inspect.signature(func)
        fields = {}
        
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
                
            field_info = {
                'default': ... if param.default == param.empty else param.default,
                'description': f"{param_name} parameter"
            }
            
            # Handle type hints
            if param.annotation != param.empty:
                field_info['type'] = param.annotation
            
            fields[param_name] = (param.annotation, Field(**field_info))
        
        # Create input schema
        input_schema = type(
            f"{tool_name.capitalize()}Input",
            (ToolInput,),
            {'__annotations__': {k: v[0] for k, v in fields.items()},
             **{k: v[1] for k, v in fields.items()}}
        )
        
        # Create tool class
        class FunctionTool(BaseTool):
            name = tool_name
            description = tool_description
            
            def __init__(self):
                super().__init__()
                # Create the input schema class
                self._input_schema = type(
                    f"{tool_name.capitalize()}Input",
                    (ToolInput,),
                    {'__annotations__': {k: v[0] for k, v in fields.items()},
                     **{k: v[1] for k, v in fields.items()}}
                )
            
            @property
            def input_schema(self):
                return self._input_schema
            
            async def _execute(self, input_data: ToolInput) -> Any:
                # Call the original function with the input data
                if inspect.iscoroutinefunction(func):
                    result = await func(**input_data.dict())
                else:
                    result = func(**input_data.dict())
                return result
        
        # Register the tool
        return ToolRegistry.register(FunctionTool())
    
    return decorator
