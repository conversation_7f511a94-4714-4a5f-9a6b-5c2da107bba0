"""Tools for the agent system.

This module provides a collection of tools that can be used by the agent system.
Tools are functions that can be called by the agent to perform specific tasks
or retrieve information.
"""
from typing import Dict, List, Any, Optional
import logging

# Import base classes and utilities
from .base import (
    BaseTool, ToolInput, ToolOutput, ToolError, ValidationError, ToolExecutionError,
    ToolRegistry, tool
)
from .utils import (
    discover_tools, get_tool_spec, get_tool_specs,
    get_tool_by_name, get_all_tools
)

# Import tool implementations
from .hotel_tools import HOTEL_TOOLS, update_room_price, get_room_prices

__all__ = [
    # Base classes
    'BaseTool', 'ToolInput', 'ToolOutput', 'ToolError', 'ValidationError',
    'ToolExecutionError', 'ToolRegistry', 'tool',
    
    # Utilities
    'discover_tools', 'get_tool_spec', 'get_tool_specs',
    'get_tool_by_name', 'get_all_tools',
    
    # Legacy exports
    'HOTEL_TOOLS', 'update_room_price', 'get_room_prices'
]

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
