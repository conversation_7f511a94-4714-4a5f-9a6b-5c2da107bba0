"""Utility functions for working with tools."""
from typing import Dict, List, Type, Any, Optional
import importlib
import pkgutil
import inspect
import logging

from .base import BaseTool, ToolRegistry

logger = logging.getLogger(__name__)

def discover_tools(package_path: str) -> Dict[str, BaseTool]:
    """Discover and register all tools in the specified package.
    
    Args:
        package_path: Dotted path to the package containing tools
        
    Returns:
        Dict mapping tool names to tool instances
    """
    try:
        package = importlib.import_module(package_path)
        
        # Get all modules in the package
        modules = []
        for _, name, _ in pkgutil.iter_modules(package.__path__):
            if name != 'base' and name != 'utils':
                modules.append(f"{package_path}.{name}")
        
        # Import all modules to trigger tool registration
        for module_name in modules:
            try:
                importlib.import_module(module_name)
                logger.debug(f"Imported tool module: {module_name}")
            except ImportError as e:
                logger.warning(f"Failed to import tool module {module_name}: {e}")
        
        return ToolRegistry.get_tools()
        
    except ImportError as e:
        logger.error(f"Failed to discover tools in {package_path}: {e}")
        return {}

def get_tool_spec(tool: BaseTool) -> Dict[str, Any]:
    """Get the specification for a tool.
    
    Args:
        tool: The tool to get the spec for
        
    Returns:
        Dict containing the tool's specification
    """
    if not isinstance(tool, BaseTool):
        raise ValueError("Tool must be an instance of BaseTool")
    
    # Get the input schema fields
    input_schema = tool.input_schema.schema()
    parameters = {
        "type": "object",
        "properties": {},
        "required": []
    }
    
    # Map the input schema to the OpenAI function calling format
    for name, prop in input_schema.get("properties", {}).items():
        param = {
            "type": prop.get("type", "string"),
            "description": prop.get("description", ""),
        }
        
        # Handle enums
        if "enum" in prop:
            param["enum"] = prop["enum"]
        
        # Handle default values
        if "default" in prop:
            param["default"] = prop["default"]
        
        parameters["properties"][name] = param
        
        # Add to required if not optional
        if name not in input_schema.get("required", []) and "default" not in prop:
            parameters["required"].append(name)
    
    return {
        "name": tool.name,
        "description": tool.description,
        "parameters": parameters
    }

def get_tool_specs(tools: List[BaseTool]) -> List[Dict[str, Any]]:
    """Get specifications for multiple tools.
    
    Args:
        tools: List of tools to get specs for
        
    Returns:
        List of tool specifications
    """
    return [get_tool_spec(tool) for tool in tools]

def get_tool_by_name(name: str) -> Optional[BaseTool]:
    """Get a tool by name.
    
    Args:
        name: Name of the tool to get
        
    Returns:
        The tool instance, or None if not found
    """
    return ToolRegistry.get_tool(name)

def get_all_tools() -> Dict[str, BaseTool]:
    """Get all registered tools.
    
    Returns:
        Dict mapping tool names to tool instances
    """
    return ToolRegistry.get_tools()
