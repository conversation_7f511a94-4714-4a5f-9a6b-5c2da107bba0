from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from agents import function_tool
import json

from app.core.supabase_client import supabase
from app.crud import hotel as hotel_crud

# Pydantic models for tool inputs
class UpdateRoomPriceInput(BaseModel):
    hotel_name: str = Field(..., description="Name of the hotel (e.g., 'Taj Hotel')")
    room_type: str = Field(..., description="Type of room (e.g., 'Deluxe King')")
    new_price: float = Field(..., description="New price per night")
    start_date: Optional[str] = Field(None, description="Start date for price change (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date for price change (YYYY-MM-DD)")

class GetRoomPriceInput(BaseModel):
    hotel_name: str = Field(..., description="Name of the hotel")
    room_type: Optional[str] = Field(None, description="Type of room (optional)")

class ListHotelsInput(BaseModel):
    location: Optional[str] = Field(None, description="Optional location to filter hotels by city or area")

@function_tool
def update_room_price(args: UpdateRoomPriceInput) -> str:
    """
    Update the price for a specific room type in a hotel.
    
    Args:
        hotel_name: Name of the hotel (e.g., 'Taj Hotel')
        room_type: Type of room (e.g., 'Deluxe King')
        new_price: New price per night
        start_date: Optional start date for price change (YYYY-MM-DD)
        end_date: Optional end date for price change (YYYY-MM-DD)
        
    Returns:
        str: Confirmation message with update details
    """
    try:
        # Find the hotel
        hotel_response = supabase.table("hotels").select("id").ilike("name", f"%{args.hotel_name}%").execute()
        if not hotel_response.data:
            return f"Error: Hotel '{args.hotel_name}' not found."
        hotel_id = hotel_response.data[0]['id']

        # Find the room type by name
        rt_response = supabase.table("room_types").select("id, base_price").ilike("name", f"%{args.room_type}%").execute()
        if not rt_response.data:
            return f"Error: Room type '{args.room_type}' not found in the system."
        room_type_id = rt_response.data[0]['id']
        old_price = rt_response.data[0]['base_price']

        # Verify this room type exists at the specified hotel
        room_exists_response = supabase.table("rooms").select("id").eq("hotel_id", hotel_id).eq("room_type_id", room_type_id).execute()
        if not room_exists_response.data:
            return f"Error: Room type '{args.room_type}' is not available at {args.hotel_name}."

        # Update the price
        update_response = supabase.table("room_types").update({"base_price": args.new_price}).eq("id", room_type_id).execute()

        if not update_response.data:
            return f"Error updating room price."

        # Prepare response
        date_range = f" from {args.start_date} to {args.end_date}" if args.start_date and args.end_date else ""
        return (
            f"Successfully updated {args.room_type} price at {args.hotel_name}{date_range}. "
            f"Price changed from ${old_price:.2f} to ${args.new_price:.2f} per night."
        )
        
    except Exception as e:
        return f"Error updating room price: {str(e)}"

def generate_update_room_price_form(hotel_name: str = None, room_type: str = None) -> str:
    """
    Generate a dynamic form for updating room prices.
    This is a direct function that can be called by the agent service.
    
    Args:
        hotel_name: Optional hotel name to pre-fill
        room_type: Optional room type to pre-fill
        
    Returns:
        str: JSON string containing form specification
    """
    try:
        hotels_response = supabase.table("hotels").select("name").execute()
        room_types_response = supabase.table("room_types").select("name, base_price").execute()

        form_spec = {
            "response_type": "form_spec",
            "form_spec": {
                "title": "Update Room Price",
                "description": "Update the price for a specific room type in a hotel.",
                "fields": [
                    {
                        "name": "hotel_name",
                        "label": "Hotel Name",
                        "type": "select",
                        "required": True,
                        "value": hotel_name or "",
                        "options": [
                            {"value": hotel['name'], "label": hotel['name']}
                            for hotel in hotels_response.data
                        ]
                    },
                    {
                        "name": "room_type",
                        "label": "Room Type",
                        "type": "select",
                        "required": True,
                        "value": room_type or "",
                        "options": [
                            {"value": rt['name'], "label": f"{rt['name']} (${rt['base_price']:.2f})"}
                            for rt in room_types_response.data
                        ]
                    },
                    {
                        "name": "new_price",
                        "label": "New Price (per night)",
                        "type": "number",
                        "required": True,
                        "min": 0,
                        "step": 0.01
                    },
                    {
                        "name": "start_date",
                        "label": "Start Date (optional)",
                        "type": "date",
                        "required": False
                    },
                    {
                        "name": "end_date",
                        "label": "End Date (optional)",
                        "type": "date",
                        "required": False
                    }
                ],
                "submit_method": "POST",
                "submit_button_text": "Update Price"
            }
        }
        
        return json.dumps(form_spec, indent=2)
        
    except Exception as e:
        error_spec = {
            "response_type": "error",
            "error": f"Error generating form: {str(e)}"
        }
        return json.dumps(error_spec)

@function_tool
def get_update_room_price_form(hotel_name: str = None, room_type: str = None) -> str:
    """
    Get a dynamic form for updating room prices.
    This function returns a structured form specification that can be rendered by the frontend.
    
    Args:
        hotel_name: Optional hotel name to pre-fill
        room_type: Optional room type to pre-fill
        
    Returns:
        str: JSON string containing form specification
    """
    return generate_update_room_price_form(hotel_name, room_type)

@function_tool
def get_room_prices(args: GetRoomPriceInput) -> str:
    """
    Get current prices and details for rooms in a hotel.
    
    This tool should be used whenever information about room availability, 
    pricing, or features is requested. Always use this tool to get the most
    up-to-date information about hotel rooms.
    
    Args:
        hotel_name: Name of the hotel (required)
        room_type: Optional filter for specific room type
        
    Returns:
        str: Formatted string with room details and prices, or error message
    """
    try:
        hotel_response = supabase.table("hotels").select("*").ilike("name", f"%{args.hotel_name}%").execute()
        if not hotel_response.data:
            return f"❌ Error: Hotel '{args.hotel_name}' not found. Please check the hotel name and try again."
        hotel = hotel_response.data[0]

        query = supabase.table("room_types").select("*, rooms!inner(*)").eq("rooms.hotel_id", hotel['id'])
        if args.room_type:
            query = query.ilike("name", f"%{args.room_type}%")
        
        room_types_response = query.order("base_price").execute()
        
        if not room_types_response.data:
            room_type_filter = f' with room type matching "{args.room_type}"' if args.room_type else ''
            return f"❌ No room types found{room_type_filter} at {hotel['name']}."
            
        result = [
            f"🏨 *{hotel['name']}* - Room Availability",
            "=" * 50,
            f"📍 {hotel.get('location', 'Location not specified')}",
            "",
            "Available Room Types:",
            ""
        ]
        
        for rt in room_types_response.data:
            room_details = [
                f"🛏️  *{rt['name']}*",
                f"   • Price: ${rt['base_price']:.2f} per night",
                f"   • Max Occupancy: {rt['max_occupancy']} people"
            ]
            
            if rt.get('description'):
                room_details.append(f"   • Description: {rt['description']}")
                
            if rt.get('image_url'):
                room_details.append(f"   • Image: {rt['image_url']}")
                
            room_details.append("")
            
            result.extend(room_details)
        
        result.extend([
            "",
            "💡 Tip: Would you like to check availability for specific dates or book a room?",
            ""
        ])
        
        return "\n".join(result)
        
    except Exception as e:
        return f"❌ Error retrieving room prices: {str(e)}"

@function_tool
def list_hotels(args: ListHotelsInput) -> str:
    """
    List all available hotels in the system, with an optional location filter.
    
    This tool should be used when the user asks about available hotels or wants to see
    what hotels are in the system. It provides a comprehensive list with basic details.
    
    Args:
        location: Optional location filter (city or area)
        
    Returns:
        str: Formatted string with hotel information
    """
    try:
        query = supabase.table("hotels").select("*")
        if args.location:
            query = query.ilike("location", f"%{args.location}%")
        
        hotels_response = query.order("name").execute()
        
        if not hotels_response.data:
            location_msg = f" in {args.location}" if args.location else ""
            return f"No hotels found{location_msg}. Please try a different location or check back later."
        
        result = ["Here are the available hotels:"]
        for hotel in hotels_response.data:
            result.append(
                f"- {hotel['name']} ({hotel['location']})\n  {hotel['description']}"
            )
            
        return "\n\n".join(result)
        
    except Exception as e:
        return "I encountered an error while retrieving the hotel list. Please try again later."

# List of all tools to be imported by the agent
HOTEL_TOOLS = [
    update_room_price,  
    get_update_room_price_form, 
    get_room_prices, 
    list_hotels
]
