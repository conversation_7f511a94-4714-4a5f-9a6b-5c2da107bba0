# HM Backend API

A FastAPI backend for the HM project built with Poetry for dependency management.

## Features

- FastAPI framework with automatic API documentation
- Poetry for dependency management
- CORS configuration for frontend integration
- Health check endpoints
- Sample CRUD operations for items
- **AI Agents with Azure OpenAI**
- Pydantic schemas for data validation
- Environment-based configuration

## Prerequisites

- Python 3.9+
- Poetry

## Installation

1. Clone the repository and navigate to the backend directory:

```bash
cd backend
```

2. Install dependencies using Poetry:

```bash
poetry install
```

3. Copy the environment example file:

```bash
cp env.example .env
```

4. Edit the `.env` file with your configuration values.

### Azure OpenAI Setup

The backend uses Azure OpenAI for AI agents. Configure your Azure OpenAI credentials:

1. Copy the environment example:

   ```bash
   cp env.example .env
   ```

2. Add your Azure OpenAI credentials to `.env`:

   ```
   AZURE_OPENAI_API_KEY=your-azure-openai-api-key
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_DEPLOYMENT=your-deployment-name
   AZURE_OPENAI_MODEL=your-model-name
   AZURE_OPENAI_API_VERSION=2025-03-01-preview
   ```

3. Example configuration:
   ```
   AZURE_OPENAI_API_KEY=your-api-key-here
   AZURE_OPENAI_ENDPOINT=https://ai-relivemistral608634573242.openai.azure.com/
   AZURE_OPENAI_DEPLOYMENT=o4-mini
   AZURE_OPENAI_MODEL=o4-mini
   AZURE_OPENAI_API_VERSION=2025-03-01-preview
   ```

## Running the Application

### Development Mode

```bash
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Production Mode

```bash
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## API Documentation

Once the server is running, you can access:

- **Interactive API Documentation (Swagger UI)**: http://localhost:8000/docs
- **Alternative API Documentation (ReDoc)**: http://localhost:8000/redoc
- **OpenAPI JSON Schema**: http://localhost:8000/openapi.json

## API Endpoints

### Health Check

- `GET /health` - Basic health check
- `GET /api/v1/health/` - API health check
- `GET /api/v1/health/detailed` - Detailed health check

### Items (Sample CRUD)

- `GET /api/v1/items/` - Get all items
- `GET /api/v1/items/{item_id}` - Get specific item
- `POST /api/v1/items/` - Create new item
- `PUT /api/v1/items/{item_id}` - Update item
- `DELETE /api/v1/items/{item_id}` - Delete item

### AI Agents

- `POST /api/v1/agents/chat` - Chat with an AI agent
- `GET /api/v1/agents/agents` - Get available agent types
- `POST /api/v1/agents/sessions` - Create a new chat session
- `DELETE /api/v1/agents/sessions/{session_id}` - Clear session history
- `GET /api/v1/agents/health` - Agent service health check
- `GET /api/v1/agents/test` - Test agent functionality

## Project Structure

```
backend/
├── app/
│   ├── api/
│   │   ├── endpoints/     # API endpoint modules
│   │   └── routes.py      # Main API router
│   ├── core/
│   │   └── config.py      # Configuration settings
│   ├── models/            # Database models (future)
│   ├── schemas/           # Pydantic schemas
│   ├── services/          # Business logic (future)
│   └── main.py           # FastAPI application
├── pyproject.toml        # Poetry configuration
├── env.example           # Environment variables example
└── README.md            # This file
```

## Development

### Adding New Endpoints

1. Create a new file in `app/api/endpoints/`
2. Define your router and endpoints
3. Include the router in `app/api/routes.py`

### Adding New Schemas

1. Create a new file in `app/schemas/`
2. Define your Pydantic models
3. Import and use in your endpoints

## Environment Variables

- `API_V1_STR`: API version string
- `PROJECT_NAME`: Project name
- `BACKEND_CORS_ORIGINS`: Allowed CORS origins
- `SECRET_KEY`: Secret key for JWT tokens
- `ACCESS_TOKEN_EXPIRE_MINUTES`: JWT token expiration time
- `AZURE_OPENAI_API_KEY`: Azure OpenAI API key for AI agents
- `AZURE_OPENAI_ENDPOINT`: Azure OpenAI endpoint URL
- `AZURE_OPENAI_DEPLOYMENT`: Azure OpenAI deployment name
- `AZURE_OPENAI_MODEL`: Azure OpenAI model name
- `DATABASE_URL`: Database connection URL

## Next Steps

- [ ] Add database integration (SQLAlchemy + PostgreSQL)
- [ ] Implement authentication and authorization
- [ ] Add logging configuration
- [ ] Set up testing framework
- [ ] Add Docker support
- [ ] Implement rate limiting
- [ ] Add API versioning

poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
