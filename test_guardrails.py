#!/usr/bin/env python3

import sys
import os
import asyncio
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.agent_service import agent_service

async def test_guardrails():
    """Test that guardrails force JSON form responses"""
    try:
        # Initialize the agent service
        await agent_service.initialize()
        
        # Test messages that should trigger form responses
        test_messages = [
            "give me form so that I can update it",
            "I want to update room prices",
            "show me a form to update prices"
        ]
        
        for message in test_messages:
            print(f"\n{'='*60}")
            print(f"Testing message: '{message}'")
            print(f"{'='*60}")
            
            # Get agent response
            response = await agent_service.chat(message)
            
            print(f"Response: {response['response'][:200]}...")
            
            # Check if response contains JSON form spec
            try:
                # Try to parse as JSON
                parsed = json.loads(response['response'])
                if 'response_type' in parsed and parsed['response_type'] == 'form_spec':
                    print("✅ Guardrail working! Agent returned JSON form spec!")
                    print(f"Form title: {parsed['form_spec']['title']}")
                    print(f"Number of fields: {len(parsed['form_spec']['fields'])}")
                else:
                    print("❌ Response is JSON but not a form spec")
            except json.JSONDecodeError:
                print("❌ Response is not valid JSON")
                print("This means the guardrail didn't trigger or the fallback didn't work")
                
            # Check metadata for guardrail info
            if 'guardrail_triggered' in response.get('metadata', {}):
                print("✅ Guardrail was triggered and fallback used!")
            else:
                print("ℹ️  Guardrail was not triggered (agent returned proper JSON)")
                
    except Exception as e:
        print(f"❌ Error testing guardrails: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_guardrails()) 