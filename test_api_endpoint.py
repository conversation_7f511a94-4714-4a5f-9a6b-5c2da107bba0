#!/usr/bin/env python3
"""
Test script to verify API endpoint functionality
"""

import asyncio
import json
import httpx
from app.services.agent_service import agent_service

async def test_api_endpoint():
    """Test the API endpoint functionality"""
    
    print("🧪 Testing API Endpoint")
    print("=" * 50)
    
    # Initialize the agent service
    await agent_service.initialize()
    
    # Test the chat endpoint directly
    test_message = "give me form"
    
    try:
        # Call the agent service directly
        result = await agent_service.chat(
            message=test_message,
            agent_type="hotel_manager"
        )
        
        print(f"✅ API Response received")
        print(f"Session ID: {result['session_id']}")
        print(f"Agent Type: {result['agent_type']}")
        
        # Check if it's a form response
        if isinstance(result['response'], dict) and result['response'].get('response_type') == 'form_spec':
            print("🎉 SUCCESS: Form response detected!")
            print(f"Form Title: {result['response']['form_spec']['title']}")
            print(f"Number of Fields: {len(result['response']['form_spec']['fields'])}")
            
            # Print the JSON structure for verification
            print("\n📋 JSON Response Structure:")
            print(json.dumps(result['response'], indent=2))
            
        else:
            print("❌ Response is not a form spec")
            print(f"Response: {result['response']}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 API Test completed!")

if __name__ == "__main__":
    asyncio.run(test_api_endpoint()) 