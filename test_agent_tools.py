import asyncio
from app.services.agent_service import AgentService

async def test_agent_tools():
    # Initialize the agent service
    agent_service = AgentService()
    
    # Test queries
    test_queries = [
        "What rooms are available at Mountain View Lodge?",
        "Show me room prices for Grand Plaza Hotel",
        "Tell me about the rooms at Seaside Resort & Spa"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing query: {query}")
        print("-" * 50)
        
        try:
            # Get response from the hotel manager agent
            response = await agent_service.chat(
                message=query,
                agent_type="hotel_manager"
            )
            
            # Print the response
            print(f"🤖 Agent Response:")
            print(response["response"])
            
            # Print any tool calls that were made
            if "tool_calls" in response and response["tool_calls"]:
                print("\n🔧 Tool Calls:")
                for i, tool_call in enumerate(response["tool_calls"], 1):
                    print(f"{i}. {tool_call['tool_name']}")
                    print(f"   Args: {tool_call['args']}")
                    print(f"   Result: {tool_call['result']}")
            else:
                print("\n⚠️ No tool calls were made")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        print("=" * 50)

if __name__ == "__main__":
    asyncio.run(test_agent_tools())
