#!/usr/bin/env python3
"""
Get the sample hotel ID for testing.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.supabase_client import supabase_service

def get_sample_hotel_id():
    """Get the sample hotel ID."""
    try:
        # Get the hotel integration
        result = supabase_service.table("hotel_integrations").select("hotel_id, id").limit(1).execute()
        
        if result.data:
            hotel_id = result.data[0]["hotel_id"]
            integration_id = result.data[0]["id"]
            
            print(f"\n🏨 Sample Hotel Information:")
            print(f"Hotel ID: {hotel_id}")
            print(f"Integration ID: {integration_id}")
            print(f"\n📝 Use this Hotel ID in your frontend instead of 'mock-hotel-id'")
            print(f"\n🔗 Test URLs:")
            print(f"Dashboard: GET /agents/monitoring/dashboard")
            print(f"Operations: GET /agents/monitoring/operations?hotel_id={hotel_id}")
            print(f"Queue: GET /agents/monitoring/queue")
            
            return hotel_id
        else:
            print("❌ No hotel integrations found. Run seed_integration_data.py first.")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    get_sample_hotel_id()
