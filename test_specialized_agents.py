#!/usr/bin/env python3
"""
Test script for specialized agents to verify they work correctly and are fast.
"""

import asyncio
import time
from app.agents.specialized_agents import specialized_agent_system, get_specialized_agent

def test_agent_selection():
    """Test that the right agents are selected for different messages."""
    print("🧪 Testing Agent Selection")
    print("=" * 50)
    
    test_cases = [
        ("list hotels", "hotel_listing"),
        ("show me available hotels", "hotel_listing"),
        ("what hotels do you have", "hotel_listing"),
        ("update room price", "price_update"),
        ("change price for deluxe room", "price_update"),
        ("I want to modify hotel prices", "price_update"),
        ("what's the price of rooms", "room_info"),
        ("show me room details", "room_info"),
        ("how much does a room cost", "room_info"),
        ("hello", "general"),
    ]
    
    for message, expected_intent in test_cases:
        start_time = time.time()
        
        # Test intent detection
        detected_intent = specialized_agent_system.analyze_user_message(message)
        
        # Test agent selection
        agent = get_specialized_agent(message)
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000
        
        status = "✅" if detected_intent == expected_intent else "❌"
        print(f"{status} '{message}' → {detected_intent} → {agent.name} ({response_time:.1f}ms)")

def test_agent_tools():
    """Test that each agent has only the tools it needs."""
    print("\n🔧 Testing Agent Tools")
    print("=" * 50)
    
    # Test hotel listing agent
    agent = specialized_agent_system._get_hotel_listing_agent()
    print(f"Hotel Listing Agent: {len(agent.tools)} tool(s)")
    for tool in agent.tools:
        print(f"  - {tool.__name__ if hasattr(tool, '__name__') else str(tool)}")
    
    # Test price update agent
    agent = specialized_agent_system._get_price_update_agent()
    print(f"Price Update Agent: {len(agent.tools)} tool(s)")
    for tool in agent.tools:
        print(f"  - {tool.__name__ if hasattr(tool, '__name__') else str(tool)}")
    
    # Test room info agent
    agent = specialized_agent_system._get_room_info_agent()
    print(f"Room Info Agent: {len(agent.tools)} tool(s)")
    for tool in agent.tools:
        print(f"  - {tool.__name__ if hasattr(tool, '__name__') else str(tool)}")
    
    # Test triage agent
    agent = specialized_agent_system._get_triage_agent()
    print(f"Triage Agent: {len(agent.handoffs)} handoff(s), {len(agent.tools)} tool(s)")
    for handoff in agent.handoffs:
        print(f"  - Handoff to: {handoff.name}")

def test_performance():
    """Test the performance of agent creation."""
    print("\n⚡ Testing Performance")
    print("=" * 50)
    
    messages = [
        "list hotels",
        "update room price", 
        "show room prices",
        "what hotels are available"
    ]
    
    total_times = []
    
    for message in messages:
        times = []
        for i in range(5):  # Test 5 times each
            start_time = time.time()
            agent = get_specialized_agent(message)
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            times.append(response_time)
        
        avg_time = sum(times) / len(times)
        total_times.extend(times)
        print(f"'{message}' → {avg_time:.1f}ms avg (range: {min(times):.1f}-{max(times):.1f}ms)")
    
    overall_avg = sum(total_times) / len(total_times)
    print(f"\nOverall average: {overall_avg:.1f}ms")
    
    if overall_avg < 50:  # Should be very fast
        print("✅ Performance: EXCELLENT (< 50ms)")
    elif overall_avg < 100:
        print("✅ Performance: GOOD (< 100ms)")
    else:
        print("⚠️  Performance: NEEDS IMPROVEMENT (> 100ms)")

async def test_agent_responses():
    """Test that agents can actually respond (requires OpenAI connection)."""
    print("\n🤖 Testing Agent Responses (requires OpenAI)")
    print("=" * 50)
    
    try:
        from agents import Runner
        
        # Test hotel listing agent
        print("Testing hotel listing agent...")
        agent = specialized_agent_system._get_hotel_listing_agent()
        start_time = time.time()
        
        # This will fail if no OpenAI connection, but that's OK for testing
        try:
            result = await Runner.run(agent, "list available hotels", session=None)
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"✅ Hotel listing response: {response_time:.0f}ms")
        except Exception as e:
            print(f"⚠️  Hotel listing test skipped: {str(e)[:50]}...")
        
        # Test price update agent
        print("Testing price update agent...")
        agent = specialized_agent_system._get_price_update_agent()
        start_time = time.time()
        
        try:
            result = await Runner.run(agent, "generate price update form", session=None)
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"✅ Price update response: {response_time:.0f}ms")
        except Exception as e:
            print(f"⚠️  Price update test skipped: {str(e)[:50]}...")
            
    except ImportError:
        print("⚠️  Skipping response tests (agents module not available)")

def main():
    """Run all tests."""
    print("🚀 Specialized Agents Test Suite")
    print("=" * 60)
    
    # Test agent selection logic
    test_agent_selection()
    
    # Test that agents have the right tools
    test_agent_tools()
    
    # Test performance
    test_performance()
    
    # Test actual responses (if possible)
    asyncio.run(test_agent_responses())
    
    # Show system stats
    print("\n📊 System Statistics")
    print("=" * 50)
    stats = specialized_agent_system.get_stats()
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    print("\n✅ All tests completed!")
    print("\nKey Benefits:")
    print("- Each agent loads only 1 specific tool")
    print("- No unnecessary database connections")
    print("- Fast agent selection (< 50ms)")
    print("- Proper separation of concerns")

if __name__ == "__main__":
    main()
