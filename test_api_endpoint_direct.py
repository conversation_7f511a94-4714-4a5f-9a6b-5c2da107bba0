#!/usr/bin/env python3
"""
Test script to verify API endpoint directly via HTTP
"""

import asyncio
import json
import httpx

async def test_api_endpoint_direct():
    """Test the API endpoint directly via HTTP"""
    
    print("🧪 Testing API Endpoint Directly")
    print("=" * 50)
    
    # Test the chat endpoint directly
    test_message = "give me form"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://127.0.0.1:8000/api/v1/agents/chat",
                json={
                    "message": test_message,
                    "agent_type": "hotel_manager"
                },
                headers={"Content-Type": "application/json"}
            )
            
            print(f"✅ HTTP Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API Response received")
                print(f"Session ID: {data['session_id']}")
                print(f"Agent Type: {data['agent_type']}")
                
                # Check if it's a form response
                if isinstance(data['response'], dict) and data['response'].get('response_type') == 'form_spec':
                    print("🎉 SUCCESS: Form response detected!")
                    print(f"Form Title: {data['response']['form_spec']['title']}")
                    print(f"Number of Fields: {len(data['response']['form_spec']['fields'])}")
                    
                    # Print the JSON structure for verification
                    print("\n📋 JSON Response Structure:")
                    print(json.dumps(data['response'], indent=2))
                    
                else:
                    print("❌ Response is not a form spec")
                    print(f"Response: {data['response']}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 Direct API Test completed!")

if __name__ == "__main__":
    asyncio.run(test_api_endpoint_direct()) 