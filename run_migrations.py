#!/usr/bin/env python3
"""
Simple migration runner for the integration system.
Run this script to apply database migrations.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.supabase_client import supabase_service

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def run_migration(migration_file: Path):
    """Run a single migration file."""
    try:
        logger.info(f"Running migration: {migration_file.name}")
        
        # Read the migration file
        with open(migration_file, 'r') as f:
            sql_content = f.read()
        
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for statement in statements:
            if statement:
                try:
                    # Execute the SQL statement
                    result = supabase_service.rpc('exec_sql', {'sql': statement}).execute()
                    logger.debug(f"Executed: {statement[:100]}...")
                except Exception as e:
                    # For Supabase, we'll use a different approach
                    logger.warning(f"Direct SQL execution not available in Supabase client: {e}")
                    logger.info("Please run the migration SQL manually in your Supabase dashboard")
                    break
        
        logger.info(f"Migration {migration_file.name} completed")
        
    except Exception as e:
        logger.error(f"Error running migration {migration_file.name}: {e}")
        raise


async def run_all_migrations():
    """Run all pending migrations."""
    try:
        migrations_dir = Path(__file__).parent / 'migrations'
        
        if not migrations_dir.exists():
            logger.error("Migrations directory not found")
            return
        
        # Get all .sql files in migrations directory
        migration_files = sorted(migrations_dir.glob('*.sql'))
        
        if not migration_files:
            logger.info("No migration files found")
            return
        
        logger.info(f"Found {len(migration_files)} migration files")
        
        for migration_file in migration_files:
            await run_migration(migration_file)
        
        logger.info("All migrations completed successfully!")
        
    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        raise


def print_migration_sql():
    """Print the migration SQL for manual execution."""
    try:
        migrations_dir = Path(__file__).parent / 'migrations'
        migration_files = sorted(migrations_dir.glob('*.sql'))
        
        print("\n" + "="*80)
        print("INTEGRATION SYSTEM DATABASE MIGRATION")
        print("="*80)
        print("\nSince you're using Supabase, please run the following SQL")
        print("in your Supabase SQL Editor:\n")
        
        for migration_file in migration_files:
            print(f"-- Migration: {migration_file.name}")
            print("-" * 50)
            
            with open(migration_file, 'r') as f:
                content = f.read()
                print(content)
            
            print("\n" + "-" * 50 + "\n")
        
        print("After running the SQL, your integration system will be ready!")
        print("="*80)
        
    except Exception as e:
        logger.error(f"Error reading migration files: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--print":
        print_migration_sql()
    else:
        print("\nIntegration System Migration Runner")
        print("=" * 40)
        print("\nOptions:")
        print("1. Print SQL for manual execution: python run_migrations.py --print")
        print("2. Attempt automatic migration: python run_migrations.py")
        print("\nRecommended: Use option 1 and run the SQL in Supabase dashboard")
        
        choice = input("\nDo you want to print the SQL? (y/n): ").lower().strip()
        
        if choice in ['y', 'yes']:
            print_migration_sql()
        else:
            print("\nAttempting automatic migration...")
            asyncio.run(run_all_migrations())
