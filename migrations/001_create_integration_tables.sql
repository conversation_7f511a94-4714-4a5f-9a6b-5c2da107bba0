-- Migration: Create Integration System Tables
-- Description: Add tables for hotel integration management system
-- Date: 2024-01-01

-- Integration Providers Table
CREATE TABLE IF NOT EXISTS integration_providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON>HAR(100) NOT NULL UNIQUE, -- 'booking_com', 'expedia', 'airbnb'
    display_name VARCHAR(100) NOT NULL,
    api_base_url VARCHAR(255),
    auth_type VARCHAR(50) NOT NULL, -- 'api_key', 'oauth', 'basic_auth'
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Hotel Integration Configurations
CREATE TABLE IF NOT EXISTS hotel_integrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hotel_id UUID NOT NULL, -- Reference to hotels table
    provider_id UUID NOT NULL REFERENCES integration_providers(id) ON DELETE CASCADE,
    is_enabled BOOLEAN DEFAULT false,
    credentials JSONB DEFAULT '{}', -- Encrypted API keys, tokens
    configuration JSONB DEFAULT '{}', -- Provider-specific settings
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'active', 'error', 'disabled'
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(hotel_id, provider_id)
);

-- Sync Operations Log
CREATE TABLE IF NOT EXISTS sync_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hotel_integration_id UUID NOT NULL REFERENCES hotel_integrations(id) ON DELETE CASCADE,
    operation_type VARCHAR(50) NOT NULL, -- 'rate_update', 'availability_update', 'inventory_sync', 'full_sync'
    direction VARCHAR(20) NOT NULL, -- 'outbound', 'inbound'
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'processing', 'success', 'failed', 'retry'
    data_payload JSONB DEFAULT '{}',
    external_reference VARCHAR(255),
    error_details JSONB,
    retry_count INTEGER DEFAULT 0,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rate and Availability Sync Queue
CREATE TABLE IF NOT EXISTS sync_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hotel_id UUID NOT NULL, -- Reference to hotels table
    operation_type VARCHAR(50) NOT NULL, -- 'rate_update', 'availability_update', 'inventory_sync', 'full_sync'
    priority INTEGER DEFAULT 5, -- 1-10, 1 being highest priority
    data JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'processing', 'success', 'failed', 'retry'
    scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_hotel_integrations_hotel_id ON hotel_integrations(hotel_id);
CREATE INDEX IF NOT EXISTS idx_hotel_integrations_provider_id ON hotel_integrations(provider_id);
CREATE INDEX IF NOT EXISTS idx_hotel_integrations_sync_status ON hotel_integrations(sync_status);

CREATE INDEX IF NOT EXISTS idx_sync_operations_hotel_integration_id ON sync_operations(hotel_integration_id);
CREATE INDEX IF NOT EXISTS idx_sync_operations_status ON sync_operations(status);
CREATE INDEX IF NOT EXISTS idx_sync_operations_operation_type ON sync_operations(operation_type);
CREATE INDEX IF NOT EXISTS idx_sync_operations_created_at ON sync_operations(created_at);

CREATE INDEX IF NOT EXISTS idx_sync_queue_hotel_id ON sync_queue(hotel_id);
CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status);
CREATE INDEX IF NOT EXISTS idx_sync_queue_priority ON sync_queue(priority);
CREATE INDEX IF NOT EXISTS idx_sync_queue_scheduled_for ON sync_queue(scheduled_for);

-- Insert default integration providers
INSERT INTO integration_providers (name, display_name, api_base_url, auth_type) VALUES
('booking_com', 'Booking.com', 'https://distribution-xml.booking.com/2.0', 'api_key'),
('expedia', 'Expedia', 'https://services.expediapartnercentral.com/eqc/ar', 'oauth'),
('airbnb', 'Airbnb', 'https://api.airbnb.com/v2', 'oauth')
ON CONFLICT (name) DO NOTHING;

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_integration_providers_updated_at 
    BEFORE UPDATE ON integration_providers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hotel_integrations_updated_at 
    BEFORE UPDATE ON hotel_integrations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE integration_providers IS 'Available integration providers (Booking.com, Expedia, etc.)';
COMMENT ON TABLE hotel_integrations IS 'Hotel-specific integration configurations';
COMMENT ON TABLE sync_operations IS 'Log of all sync operations performed';
COMMENT ON TABLE sync_queue IS 'Queue for pending sync operations';

COMMENT ON COLUMN hotel_integrations.credentials IS 'Encrypted API credentials for the provider';
COMMENT ON COLUMN hotel_integrations.configuration IS 'Provider-specific configuration settings';
COMMENT ON COLUMN sync_operations.data_payload IS 'Data being synchronized';
COMMENT ON COLUMN sync_operations.error_details IS 'Detailed error information if operation failed';
COMMENT ON COLUMN sync_queue.data IS 'Operation data and parameters';
COMMENT ON COLUMN sync_queue.priority IS 'Priority level (1=highest, 10=lowest)';
