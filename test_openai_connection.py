import os
from openai import AsyncAzureOpenAI
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get Azure OpenAI settings from environment variables
api_key = os.getenv("AZURE_OPENAI_API_KEY")
api_version = os.getenv("AZURE_OPENAI_API_VERSION")
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT")
model = os.getenv("AZURE_OPENAI_MODEL", "gpt-4")

print("🔍 Testing Azure OpenAI Configuration:")
print(f"- Endpoint: {endpoint}")
print(f"- API Version: {api_version}")
print(f"- Deployment: {deployment}")
print(f"- Model: {model}")
print(f"- API Key: {'*' * 8 + api_key[-4:] if api_key else 'Not found'}")

if not all([api_key, api_version, endpoint, deployment]):
    print("❌ Error: Missing required environment variables")
    exit(1)

# Initialize the client
client = AsyncAzureOpenAI(
    api_key=api_key,
    api_version=api_version,
    azure_endpoint=endpoint,
    azure_deployment=deployment,
)

async def test_completion():
    try:
        print("\n🚀 Testing completion...")
        response = await client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say hello!"}
            ]
        )
        print(f"✅ Success! Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Error testing completion: {str(e)}")
        return False

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_completion())
