# Hotel Integration System

A comprehensive integration system for synchronizing hotel data with external booking platforms like Booking.com, Expedia, and Airbnb.

## 🚀 Features

- **Multi-Provider Support**: Booking.com, Expedia, Airbnb integrations
- **Real-time Sync**: Rate and availability updates
- **Background Processing**: Async queue-based sync operations
- **Webhook Support**: Handle incoming updates from providers
- **Monitoring & Analytics**: Comprehensive monitoring dashboard
- **Error Handling**: Retry logic and detailed error tracking
- **Security**: Encrypted credentials and webhook signature verification

## 📋 Setup Instructions

### 1. Database Migration

Run the database migration to create the integration tables:

```bash
# Print SQL for manual execution in Supabase
python run_migrations.py --print
```

Copy the output SQL and run it in your Supabase SQL Editor.

### 2. Environment Variables

Add these to your `.env` file:

```env
# Integration System
INTEGRATION_WEBHOOK_SECRET_BOOKING=your-booking-webhook-secret
INTEGRATION_WEBHOOK_SECRET_EXPEDIA=your-expedia-webhook-secret
INTEGRATION_WEBHOOK_SECRET_AIRBNB=your-airbnb-webhook-secret
```

### 3. Start the Application

The integration system will automatically start with your FastAPI application.

## 🔧 API Endpoints

### Integration Management

```http
# Get all available providers
GET /api/v1/providers

# Get hotel integrations
GET /api/v1/hotels/{hotel_id}/integrations

# Create hotel integration
POST /api/v1/hotels/{hotel_id}/integrations
{
  "provider_id": "uuid",
  "credentials": {
    "api_key": "your-api-key",
    "property_id": "12345"
  },
  "configuration": {
    "sync_rates": true,
    "sync_availability": true
  }
}

# Update integration
PUT /api/v1/hotels/{hotel_id}/integrations/{integration_id}

# Test integration connection
POST /api/v1/hotels/{hotel_id}/integrations/{integration_id}/test
```

### Sync Operations

```http
# Trigger rate update
POST /api/v1/hotels/{hotel_id}/sync/rates
{
  "room_type_id": "uuid",
  "date_from": "2024-01-01",
  "date_to": "2024-01-31",
  "new_rate": 150.00,
  "currency": "USD",
  "providers": ["booking_com", "expedia"]
}

# Trigger availability update
POST /api/v1/hotels/{hotel_id}/sync/availability
{
  "room_type_id": "uuid",
  "date": "2024-01-01",
  "available_rooms": 5,
  "providers": ["booking_com"]
}

# Bulk sync operations
POST /api/v1/hotels/{hotel_id}/sync/bulk
{
  "operations": [
    {
      "type": "rate_update",
      "room_type_id": "uuid",
      "date_from": "2024-01-01",
      "date_to": "2024-01-07",
      "rate": 150.00
    }
  ]
}

# Full sync
POST /api/v1/hotels/{hotel_id}/sync/full

# Get sync status
GET /api/v1/hotels/{hotel_id}/sync/status
```

### Webhooks

```http
# Booking.com webhook
POST /api/v1/webhooks/booking-com

# Expedia webhook
POST /api/v1/webhooks/expedia

# Airbnb webhook
POST /api/v1/webhooks/airbnb
```

### Monitoring

```http
# Monitoring dashboard
GET /api/v1/monitoring/dashboard

# Sync operations
GET /api/v1/monitoring/operations?hotel_id=uuid&limit=50

# Operation details
GET /api/v1/monitoring/operations/{operation_id}

# Queue status
GET /api/v1/monitoring/queue

# System health
GET /api/v1/monitoring/health

# Analytics
GET /api/v1/monitoring/analytics?days=7
```

## 🏗️ Architecture

### Components

1. **Integration Providers**: Manage available booking platforms
2. **Hotel Integrations**: Hotel-specific integration configurations
3. **Sync Queue**: Background job queue for sync operations
4. **Sync Processor**: Background service processing sync operations
5. **Provider Services**: Platform-specific API implementations
6. **Webhook Handlers**: Handle incoming updates from providers
7. **Monitoring**: Track operations and system health

### Data Flow

1. **Outbound Sync**: Hotel → Queue → Processor → Provider API
2. **Inbound Updates**: Provider Webhook → Handler → Database
3. **Monitoring**: All operations logged and tracked

## 🔐 Security

- **Encrypted Credentials**: All API keys stored encrypted
- **Webhook Signatures**: Verify incoming webhook authenticity
- **Authentication**: All endpoints require valid JWT tokens
- **Rate Limiting**: Prevent API abuse

## 📊 Provider Configurations

### Booking.com

```json
{
  "credentials": {
    "api_key": "your-booking-api-key",
    "property_id": "12345"
  },
  "configuration": {
    "sync_rates": true,
    "sync_availability": true,
    "sync_inventory": false
  }
}
```

### Expedia

```json
{
  "credentials": {
    "client_id": "your-expedia-client-id",
    "client_secret": "your-expedia-client-secret",
    "property_id": "67890"
  },
  "configuration": {
    "sync_rates": true,
    "sync_availability": true
  }
}
```

### Airbnb

```json
{
  "credentials": {
    "access_token": "your-airbnb-access-token",
    "listing_id": "54321"
  },
  "configuration": {
    "sync_rates": true,
    "sync_availability": true
  }
}
```

## 🚨 Error Handling

- **Automatic Retry**: Failed operations retry with exponential backoff
- **Error Logging**: Detailed error information stored
- **Status Tracking**: Real-time operation status updates
- **Alerting**: Monitor for critical failures

## 📈 Monitoring

- **Dashboard**: Real-time system overview
- **Operation Logs**: Detailed sync operation history
- **Queue Status**: Monitor pending operations
- **Analytics**: Performance metrics and trends
- **Health Checks**: System component status

## 🔄 Background Processing

The sync processor runs continuously and:

1. Polls the sync queue for pending operations
2. Processes operations with priority ordering
3. Handles retries for failed operations
4. Updates operation status and logs
5. Manages concurrent operation limits

## 🛠️ Development

### Adding New Providers

1. Create provider service in `app/services/provider_services.py`
2. Add provider configuration schema in `app/schemas/integration.py`
3. Insert provider record in database
4. Implement webhook handler if needed

### Testing

```bash
# Test integration connection
curl -X POST "http://localhost:8000/api/v1/hotels/{hotel_id}/integrations/{integration_id}/test" \
  -H "Authorization: Bearer your-jwt-token"

# Trigger test sync
curl -X POST "http://localhost:8000/api/v1/hotels/{hotel_id}/sync/rates" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "room_type_id": "uuid",
    "date_from": "2024-01-01",
    "date_to": "2024-01-07",
    "new_rate": 150.00
  }'
```

## 📝 Next Steps

1. Run the database migration
2. Configure your first integration
3. Test the sync operations
4. Set up monitoring alerts
5. Configure webhooks with providers

For support or questions, check the API documentation at `/docs` when running the application.
