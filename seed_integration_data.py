#!/usr/bin/env python3
"""
Seed script to populate integration providers and sample data.
"""

from app.core.supabase_client import supabase_service
import asyncio
import sys
import os
import logging
from uuid import uuid4

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def seed_integration_providers():
    """Seed the integration providers table."""
    try:
        logger.info("Seeding integration providers...")

        providers = [
            {
                "name": "booking_com",
                "display_name": "Booking.com",
                "api_base_url": "https://distribution-xml.booking.com/2.0",
                "auth_type": "api_key",
                "is_active": True
            },
            {
                "name": "expedia",
                "display_name": "Expedia",
                "api_base_url": "https://services.expediapartnercentral.com/eqc/ar",
                "auth_type": "oauth",
                "is_active": True
            },
            {
                "name": "airbnb",
                "display_name": "Airbnb",
                "api_base_url": "https://api.airbnb.com/v2",
                "auth_type": "oauth",
                "is_active": True
            }
        ]

        for provider in providers:
            try:
                # Check if provider already exists
                existing = supabase_service.table("integration_providers").select(
                    "id").eq("name", provider["name"]).execute()

                if existing.data:
                    logger.info(
                        f"Provider {provider['name']} already exists, skipping...")
                    continue

                # Insert provider
                result = supabase_service.table(
                    "integration_providers").insert(provider).execute()

                if result.data:
                    logger.info(
                        f"✅ Created provider: {provider['display_name']}")
                else:
                    logger.error(
                        f"❌ Failed to create provider: {provider['name']}")

            except Exception as e:
                logger.error(
                    f"Error creating provider {provider['name']}: {e}")

        logger.info("Integration providers seeding completed!")

    except Exception as e:
        logger.error(f"Error seeding integration providers: {e}")
        raise


async def seed_sample_hotel_integration():
    """Create a sample hotel integration for testing."""
    try:
        logger.info("Creating sample hotel integration...")

        # Get booking.com provider
        provider_result = supabase_service.table("integration_providers").select(
            "id").eq("name", "booking_com").execute()

        if not provider_result.data:
            logger.error(
                "Booking.com provider not found. Run seed_integration_providers first.")
            return

        provider_id = provider_result.data[0]["id"]

        # Create sample hotel integration
        sample_hotel_id = str(uuid4())  # Generate a proper UUID
        logger.info(f"Using hotel ID: {sample_hotel_id}")

        # Check if integration already exists
        existing = supabase_service.table("hotel_integrations").select("id").eq(
            "hotel_id", sample_hotel_id).eq("provider_id", provider_id).execute()

        if existing.data:
            logger.info("Sample hotel integration already exists, skipping...")
            return

        integration_data = {
            "hotel_id": sample_hotel_id,
            "provider_id": provider_id,
            "is_enabled": True,
            "credentials": {
                "api_key": "sample-api-key",
                "property_id": "12345"
            },
            "configuration": {
                "sync_rates": True,
                "sync_availability": True,
                "sync_inventory": False
            },
            "sync_status": "active"
        }

        result = supabase_service.table(
            "hotel_integrations").insert(integration_data).execute()

        if result.data:
            logger.info(
                f"✅ Created sample hotel integration: {result.data[0]['id']}")
            return result.data[0]["id"]
        else:
            logger.error("❌ Failed to create sample hotel integration")

    except Exception as e:
        logger.error(f"Error creating sample hotel integration: {e}")
        raise


async def seed_sample_sync_operations():
    """Create sample sync operations for testing."""
    try:
        logger.info("Creating sample sync operations...")

        # Get the sample hotel integration (get the first one we can find)
        integration_result = supabase_service.table("hotel_integrations").select(
            "id, hotel_id").limit(1).execute()

        if not integration_result.data:
            logger.error(
                "No hotel integrations found. Run seed_sample_hotel_integration first.")
            return

        integration_id = integration_result.data[0]["id"]
        hotel_id = integration_result.data[0]["hotel_id"]
        logger.info(f"Using integration {integration_id} for hotel {hotel_id}")

        # Create sample sync operations
        operations = [
            {
                "hotel_integration_id": integration_id,
                "operation_type": "rate_update",
                "direction": "outbound",
                "status": "success",
                "data_payload": {
                    "room_type_id": "sample-room-type",
                    "date_from": "2024-01-01",
                    "date_to": "2024-01-07",
                    "rate": 150.00
                },
                "retry_count": 0
            },
            {
                "hotel_integration_id": integration_id,
                "operation_type": "availability_update",
                "direction": "outbound",
                "status": "success",
                "data_payload": {
                    "room_type_id": "sample-room-type",
                    "date": "2024-01-01",
                    "available_rooms": 5
                },
                "retry_count": 0
            },
            {
                "hotel_integration_id": integration_id,
                "operation_type": "rate_update",
                "direction": "outbound",
                "status": "failed",
                "data_payload": {
                    "room_type_id": "sample-room-type",
                    "date_from": "2024-01-08",
                    "date_to": "2024-01-14",
                    "rate": 175.00
                },
                "error_details": {
                    "error": "API rate limit exceeded",
                    "code": "RATE_LIMIT"
                },
                "retry_count": 2
            }
        ]

        for operation in operations:
            result = supabase_service.table(
                "sync_operations").insert(operation).execute()

            if result.data:
                logger.info(
                    f"✅ Created sync operation: {operation['operation_type']} - {operation['status']}")
            else:
                logger.error(
                    f"❌ Failed to create sync operation: {operation['operation_type']}")

        logger.info("Sample sync operations created!")

    except Exception as e:
        logger.error(f"Error creating sample sync operations: {e}")
        raise


async def main():
    """Main seeding function."""
    try:
        logger.info("Starting integration data seeding...")

        # Seed integration providers
        await seed_integration_providers()

        # Create sample hotel integration
        await seed_sample_hotel_integration()

        # Create sample sync operations
        await seed_sample_sync_operations()

        logger.info("🎉 All seeding completed successfully!")

    except Exception as e:
        logger.error(f"Seeding failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
