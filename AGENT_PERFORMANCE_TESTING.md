# AI Agent Performance Testing & Isolation

This document explains how to isolate and test AI agent performance to identify bottlenecks causing slow response times.

## Problem

You're experiencing 25-30 second response times for simple messages like "Hi" to your AI agent, which is unacceptable for production use.

## Solution: Performance Isolation Testing

I've created isolated test endpoints that bypass different layers of your application to pinpoint exactly where the bottleneck is occurring.

## Test Endpoints

### 1. Agent Health Check
**Endpoint**: `GET /api/v1/test/test-agent-health`
**Purpose**: Verify agent service is initialized and healthy
**What it tests**: Basic agent availability and initialization

```bash
curl http://localhost:8000/api/v1/test/test-agent-health
```

### 2. Direct Agent Test (No Auth, No Storage)
**Endpoint**: `POST /api/v1/test/test-agent-only`
**Purpose**: Test pure AI agent performance without any overhead
**What it bypasses**: Authentication, session management, database operations

```bash
curl -X POST http://localhost:8000/api/v1/test/test-agent-only \
  -H "Content-Type: application/json" \
  -d '{"message": "Hi", "agent_type": "hotel_manager"}'
```

### 3. Agent with Minimal Session (No Auth, No Storage)
**Endpoint**: `POST /api/v1/test/test-agent-with-minimal-session`
**Purpose**: Test agent with session handling but no database operations
**What it bypasses**: Authentication, database storage

```bash
curl -X POST http://localhost:8000/api/v1/test/test-agent-with-minimal-session \
  -H "Content-Type: application/json" \
  -d '{"message": "Hi", "agent_type": "hotel_manager"}'
```

### 4. Full Endpoint (With Auth and Storage)
**Endpoint**: `POST /api/v1/agents/chat`
**Purpose**: Test the complete flow as your frontend uses it
**What it includes**: Authentication, session management, database operations

```bash
# First get a test token
TOKEN=$(curl -s http://localhost:8000/test-token | jq -r '.access_token')

# Then test the full endpoint
curl -X POST http://localhost:8000/api/v1/agents/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"message": "Hi", "agent_type": "hotel_manager"}'
```

## Automated Testing Script

Run the comprehensive test script:

```bash
cd backend
python test_agent_isolation.py
```

This script will:
1. Test all endpoints in order
2. Measure response times for each layer
3. Identify where the bottleneck is occurring
4. Provide specific recommendations

## Expected Results & Diagnosis

### If Direct Agent Test is Slow (>5 seconds)
**Problem**: AI agent itself is the bottleneck
**Likely causes**:
- OpenAI API connection issues
- Network latency to Azure OpenAI
- Model configuration problems
- Prompt complexity

**Solutions**:
- Check OpenAI API status
- Verify Azure OpenAI endpoint configuration
- Test network connectivity
- Optimize prompts
- Consider using a faster model

### If Session Test Adds Significant Time
**Problem**: Session handling overhead
**Likely causes**:
- Database connection latency
- Session lookup operations
- History loading

**Solutions**:
- Optimize database queries
- Add connection pooling
- Cache session data

### If Full Endpoint Adds Significant Time
**Problem**: Authentication or storage operations
**Likely causes**:
- JWT token verification
- Database writes
- Tracing operations

**Solutions**:
- Optimize authentication flow
- Use background tasks for storage
- Reduce tracing overhead

## Performance Benchmarks

For a simple "Hi" message, you should expect:

| Test Level | Expected Time | What It Measures |
|------------|---------------|------------------|
| Direct Agent | 500-2000ms | Pure AI response time |
| + Session | +100-500ms | Session handling overhead |
| + Full Auth/Storage | +200-1000ms | Complete application overhead |

**Total acceptable time**: 1-3 seconds maximum

## Troubleshooting Common Issues

### 1. Agent Not Initialized
```
Error: Agent hotel_manager not found
```
**Solution**: Check agent service initialization in startup logs

### 2. OpenAI Connection Issues
```
Error: OpenAI API connection failed
```
**Solution**: Verify Azure OpenAI configuration and network connectivity

### 3. Database Connection Issues
```
Error: Supabase connection failed
```
**Solution**: Check Supabase credentials and network connectivity

### 4. Authentication Issues
```
Error: 401 Unauthorized
```
**Solution**: Verify JWT token generation and validation

## Monitoring in Production

After identifying and fixing bottlenecks, monitor these metrics:

1. **Agent Response Time**: Should be <2 seconds
2. **Database Query Time**: Should be <500ms
3. **Authentication Time**: Should be <100ms
4. **Total Request Time**: Should be <3 seconds

## Next Steps

1. Run the isolation test script
2. Identify which layer is causing the 25-30 second delay
3. Apply targeted optimizations based on the results
4. Re-test to verify improvements
5. Deploy optimizations to production

The test endpoints will remain available for ongoing performance monitoring and debugging.
