#!/usr/bin/env python3

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.agent_service import form_detection_agent
from agents import Runner

async def test_guardrail_detection():
    """Test that the guardrail detects form requests correctly"""
    try:
        # Test messages
        test_messages = [
            "give me form so that I can update it",
            "I want to update room prices",
            "show me a form to update prices",
            "What hotels do you have?",  # This should NOT be detected as form request
        ]
        
        for message in test_messages:
            print(f"\n{'='*60}")
            print(f"Testing message: '{message}'")
            print(f"{'='*60}")
            
            # Run the guardrail detection agent
            result = await Runner.run(form_detection_agent, message)
            
            print(f"Guardrail result: {result.final_output}")
            
            if "FORM_REQUEST" in result.final_output.upper():
                print("✅ Guardrail correctly detected form request!")
            else:
                print("❌ Guardrail did NOT detect form request")
                
    except Exception as e:
        print(f"❌ Error testing guardrail detection: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_guardrail_detection()) 