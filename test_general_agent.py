#!/usr/bin/env python3
"""
Test script to verify general agent form response functionality
"""

import asyncio
import json
from app.services.agent_service import agent_service

async def test_general_agent_form():
    """Test the general agent form response functionality"""
    
    print("🧪 Testing General Agent Form Response")
    print("=" * 50)
    
    # Initialize the agent service
    await agent_service.initialize()
    
    # Test cases
    test_messages = [
        "give me form",
        "show me a form to update prices",
        "I want to update room prices"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📝 Test {i}: '{message}'")
        print("-" * 30)
        
        try:
            # Send the message to the general agent
            result = await agent_service.chat(
                message=message,
                agent_type="general"
            )
            
            print(f"✅ Response received")
            print(f"Session ID: {result['session_id']}")
            print(f"Agent Type: {result['agent_type']}")
            
            # Check if it's a form response
            if isinstance(result['response'], dict) and result['response'].get('response_type') == 'form_spec':
                print("🎉 SUCCESS: Form response detected!")
                print(f"Form Title: {result['response']['form_spec']['title']}")
                print(f"Number of Fields: {len(result['response']['form_spec']['fields'])}")
                
                # Print field details
                for field in result['response']['form_spec']['fields']:
                    print(f"  - {field['name']}: {field['type']} ({'required' if field.get('required') else 'optional'})")
                    
            elif isinstance(result['response'], str):
                # Try to parse as JSON
                try:
                    parsed = json.loads(result['response'])
                    if parsed.get('response_type') == 'form_spec':
                        print("🎉 SUCCESS: Form response detected (JSON string)!")
                        print(f"Form Title: {parsed['form_spec']['title']}")
                    else:
                        print("❌ Response is not a form spec")
                        print(f"Response: {result['response'][:100]}...")
                except json.JSONDecodeError:
                    print("❌ Response is not JSON")
                    print(f"Response: {result['response'][:100]}...")
            else:
                print("❌ Unexpected response format")
                print(f"Response type: {type(result['response'])}")
                print(f"Response: {result['response']}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 General Agent Test completed!")

if __name__ == "__main__":
    asyncio.run(test_general_agent_form()) 