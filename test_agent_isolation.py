#!/usr/bin/env python3
"""
Test script to isolate AI agent performance and identify bottlenecks.
This script tests different levels of isolation to pinpoint where the 25-30 second delay is coming from.
"""

import asyncio
import time
import httpx
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_MESSAGE = "Hi"

async def test_agent_health():
    """Test if the agent service is healthy and initialized."""
    print("🔍 Testing agent health...")
    start_time = time.time()
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/api/v1/test/test-agent-health", timeout=30.0)
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Agent Health: {response_time:.2f}ms")
                print(f"   Status: {data.get('status')}")
                print(f"   Agent Initialized: {data.get('agent_initialized')}")
                print(f"   Available Agents: {data.get('available_agents')}")
                return True
            else:
                print(f"❌ Agent Health Failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"❌ Agent Health Error: {e} ({response_time:.2f}ms)")
            return False

async def test_direct_agent():
    """Test the AI agent directly without any authentication or data storage."""
    print("\n🚀 Testing direct agent (no auth, no storage)...")
    start_time = time.time()
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/test/test-agent-only",
                json={"message": TEST_MESSAGE, "agent_type": "hotel_manager"},
                timeout=60.0
            )
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Direct Agent: {response_time:.2f}ms")
                print(f"   Agent Response Time: {data.get('response_time_ms'):.2f}ms")
                print(f"   Response Length: {len(data.get('response', ''))}")
                print(f"   Agent Type: {data.get('agent_type')}")
                return data
            else:
                print(f"❌ Direct Agent Failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"❌ Direct Agent Error: {e} ({response_time:.2f}ms)")
            return None

async def test_agent_with_session():
    """Test the AI agent with minimal session handling but no data storage."""
    print("\n🔄 Testing agent with minimal session (no auth, no storage)...")
    start_time = time.time()
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/test/test-agent-with-minimal-session",
                json={"message": TEST_MESSAGE, "agent_type": "hotel_manager"},
                timeout=60.0
            )
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Agent with Session: {response_time:.2f}ms")
                print(f"   Agent Response Time: {data.get('response_time_ms'):.2f}ms")
                print(f"   Response Length: {len(data.get('response', ''))}")
                print(f"   Session ID: {data.get('session_id')}")
                return data
            else:
                print(f"❌ Agent with Session Failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"❌ Agent with Session Error: {e} ({response_time:.2f}ms)")
            return None

async def test_full_endpoint():
    """Test the full endpoint with authentication and data storage."""
    print("\n🔐 Testing full endpoint (with auth and storage)...")
    start_time = time.time()
    
    async with httpx.AsyncClient() as client:
        try:
            # First get a test token
            token_response = await client.get(f"{BASE_URL}/test-token", timeout=10.0)
            if token_response.status_code != 200:
                print(f"❌ Failed to get test token: {token_response.status_code}")
                return None
            
            token_data = token_response.json()
            access_token = token_data["access_token"]
            
            # Now test the full endpoint
            response = await client.post(
                f"{BASE_URL}/api/v1/agents/chat",
                json={"message": TEST_MESSAGE, "agent_type": "hotel_manager"},
                headers={"Authorization": f"Bearer {access_token}"},
                timeout=60.0
            )
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Full Endpoint: {response_time:.2f}ms")
                print(f"   Response Length: {len(data.get('response', ''))}")
                print(f"   Session ID: {data.get('session_id')}")
                print(f"   Agent Type: {data.get('agent_type')}")
                return data
            else:
                print(f"❌ Full Endpoint Failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"❌ Full Endpoint Error: {e} ({response_time:.2f}ms)")
            return None

async def analyze_results(health_ok: bool, direct_result: Dict[Any, Any], session_result: Dict[Any, Any], full_result: Dict[Any, Any]):
    """Analyze the test results and provide recommendations."""
    print("\n" + "="*60)
    print("📊 PERFORMANCE ANALYSIS")
    print("="*60)
    
    if not health_ok:
        print("❌ CRITICAL: Agent service is not healthy!")
        print("   Recommendation: Check agent initialization and dependencies")
        return
    
    if not direct_result:
        print("❌ CRITICAL: Direct agent call failed!")
        print("   Recommendation: Check AI agent configuration and OpenAI connection")
        return
    
    direct_time = direct_result.get('response_time_ms', 0)
    session_time = session_result.get('response_time_ms', 0) if session_result else 0
    
    print(f"✅ Agent Health: OK")
    print(f"⚡ Direct Agent Time: {direct_time:.2f}ms")
    
    if session_result:
        print(f"🔄 Agent + Session Time: {session_time:.2f}ms")
        session_overhead = session_time - direct_time
        print(f"   Session Overhead: {session_overhead:.2f}ms")
    
    if full_result:
        # We don't have the internal timing for full endpoint, so we estimate
        print(f"🔐 Full Endpoint: Available")
        print("   (Includes auth + database operations)")
    
    # Provide recommendations
    print("\n🎯 RECOMMENDATIONS:")
    
    if direct_time > 5000:  # > 5 seconds
        print("❌ CRITICAL: AI agent itself is slow (>5s)")
        print("   - Check OpenAI API connection and latency")
        print("   - Verify Azure OpenAI configuration")
        print("   - Check network connectivity to OpenAI")
        print("   - Consider using a faster model or reducing context")
    elif direct_time > 2000:  # > 2 seconds
        print("⚠️  WARNING: AI agent is slower than expected (>2s)")
        print("   - Check OpenAI API latency")
        print("   - Consider optimizing prompts")
    else:
        print("✅ AI agent performance is good (<2s)")
    
    if session_result and session_time - direct_time > 1000:  # > 1 second overhead
        print("⚠️  WARNING: Session handling adds significant overhead")
        print("   - Check database connection latency")
        print("   - Optimize session lookup operations")
    
    print(f"\n💡 For a simple 'Hi' message, expect:")
    print(f"   - Direct agent: 500-2000ms")
    print(f"   - With session: +100-500ms")
    print(f"   - With full auth/storage: +200-1000ms")

async def main():
    """Run all performance tests."""
    print("🧪 AI Agent Performance Isolation Test")
    print("="*60)
    print(f"Testing with message: '{TEST_MESSAGE}'")
    print(f"Base URL: {BASE_URL}")
    print()
    
    # Run tests in order of complexity
    health_ok = await test_agent_health()
    direct_result = await test_direct_agent()
    session_result = await test_agent_with_session()
    full_result = await test_full_endpoint()
    
    # Analyze results
    await analyze_results(health_ok, direct_result, session_result, full_result)
    
    print("\n" + "="*60)
    print("Test completed! Check the analysis above for bottlenecks.")

if __name__ == "__main__":
    asyncio.run(main())
