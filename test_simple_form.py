#!/usr/bin/env python3

import sys
import os
import asyncio
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.agent_service import agent_service

async def test_simple_form():
    """Test that the agent can return form data"""
    try:
        # Initialize the agent service
        await agent_service.initialize()
        
        # Test a simple form request
        message = "give me form so that I can update it"
        print(f"Testing message: '{message}'")
        
        # Get agent response
        response = await agent_service.chat(message)
        
        print(f"Response: {response['response'][:500]}...")
        
        # Check if response contains JSON form spec
        try:
            # Try to parse as JSON
            parsed = json.loads(response['response'])
            if 'response_type' in parsed and parsed['response_type'] == 'form_spec':
                print("✅ Success! Agent returned JSON form spec!")
                print(f"Form title: {parsed['form_spec']['title']}")
                print(f"Number of fields: {len(parsed['form_spec']['fields'])}")
                return True
            else:
                print("❌ Response is JSON but not a form spec")
                return False
        except json.JSONDecodeError:
            print("❌ Response is not valid JSON")
            print("Full response:")
            print(response['response'])
            return False
                
    except Exception as e:
        print(f"❌ Error testing agent: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_simple_form())
    if success:
        print("\n🎉 Test passed! The agent is returning proper form JSON.")
    else:
        print("\n❌ Test failed! The agent is not returning proper form JSON.") 