[tool.poetry]
name = "hm-backend"
version = "0.1.0"
description = "FastAPI backend for HM project"
authors = ["Sagar Mehta"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.116.1"
uvicorn = "^0.35.0"
python-multipart = "^0.0.20"
python-jose = "^3.5.0"
passlib = "^1.7.4"
python-dotenv = "^1.1.1"
pydantic-settings = "^2.10.1"
openai-agents = "^0.2.3"
boto3 = "^1.34.0"
langdetect = "^1.0.9"
supabase = "^2.18.1"
urllib3 = "<1.27"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
